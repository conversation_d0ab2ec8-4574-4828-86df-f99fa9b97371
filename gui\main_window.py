# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from typing import Optional, List
import threading
from datetime import datetime, timedelta
import os

from utils.config import config
from utils.logger import logger
from api.ayrshare_client import ayrshare_client
from api.rate_limiter import rate_limiter
from database.database import db
from database.models import Post

# إعداد CustomTkinter
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class MainWindow:
    """النافذة الرئيسية للتطبيق"""

    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Ayrshare Social Media Manager")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # متغيرات الواجهة
        self.api_key_var = tk.StringVar(value=config.get('api_key', ''))
        self.profile_key_var = tk.StringVar(value=config.get('profile_key', ''))
        self.content_var = tk.StringVar()
        self.hashtags_var = tk.StringVar()

        # قوائم المنصات والملفات
        self.selected_platforms = []
        self.selected_media_files = []

        # إعداد الواجهة
        self.setup_ui()
        self.load_settings()

        logger.info("تم تشغيل التطبيق")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # إنشاء التبويبات
        self.notebook = ctk.CTkTabview(main_frame)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # تبويب النشر
        self.create_post_tab()

        # تبويب الجدولة
        self.create_schedule_tab()

        # تبويب الإعدادات
        self.create_settings_tab()

        # تبويب الإحصائيات
        self.create_stats_tab()

        # شريط الحالة
        self.create_status_bar()

    def create_post_tab(self):
        """إنشاء تبويب النشر"""
        post_tab = self.notebook.add("نشر محتوى")

        # إطار إعدادات API
        api_frame = ctk.CTkFrame(post_tab)
        api_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(api_frame, text="إعدادات API", font=("Arial", 16, "bold")).pack(pady=5)

        # API Key
        api_key_frame = ctk.CTkFrame(api_frame)
        api_key_frame.pack(fill="x", padx=10, pady=2)

        ctk.CTkLabel(api_key_frame, text="API Key:", width=100).pack(side="left", padx=5)
        self.api_key_entry = ctk.CTkEntry(api_key_frame, textvariable=self.api_key_var,
                                         show="*", width=300)
        self.api_key_entry.pack(side="left", padx=5, fill="x", expand=True)

        ctk.CTkButton(api_key_frame, text="اختبار", command=self.test_api_connection,
                     width=80).pack(side="right", padx=5)

        # Profile Key
        profile_frame = ctk.CTkFrame(api_frame)
        profile_frame.pack(fill="x", padx=10, pady=2)

        ctk.CTkLabel(profile_frame, text="Profile Key:", width=100).pack(side="left", padx=5)
        self.profile_key_entry = ctk.CTkEntry(profile_frame, textvariable=self.profile_key_var,
                                             width=300)
        self.profile_key_entry.pack(side="left", padx=5, fill="x", expand=True)

        # إطار المحتوى
        content_frame = ctk.CTkFrame(post_tab)
        content_frame.pack(fill="both", expand=True, padx=10, pady=5)

        ctk.CTkLabel(content_frame, text="محتوى المنشور", font=("Arial", 16, "bold")).pack(pady=5)

        # نص المحتوى
        self.content_text = ctk.CTkTextbox(content_frame, height=150)
        self.content_text.pack(fill="x", padx=10, pady=5)

        # الهاشتاجات
        hashtags_frame = ctk.CTkFrame(content_frame)
        hashtags_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(hashtags_frame, text="الهاشتاجات:", width=100).pack(side="left", padx=5)
        self.hashtags_entry = ctk.CTkEntry(hashtags_frame, textvariable=self.hashtags_var)
        self.hashtags_entry.pack(side="left", padx=5, fill="x", expand=True)

        # إطار المنصات
        platforms_frame = ctk.CTkFrame(content_frame)
        platforms_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(platforms_frame, text="المنصات:", font=("Arial", 14, "bold")).pack(anchor="w", padx=5)

        platforms_checkboxes_frame = ctk.CTkFrame(platforms_frame)
        platforms_checkboxes_frame.pack(fill="x", padx=10, pady=5)

        # خانات اختيار المنصات
        self.facebook_var = tk.BooleanVar(value=True)
        self.instagram_var = tk.BooleanVar(value=True)
        self.tiktok_var = tk.BooleanVar(value=True)

        ctk.CTkCheckBox(platforms_checkboxes_frame, text="Facebook",
                       variable=self.facebook_var).pack(side="left", padx=10)
        ctk.CTkCheckBox(platforms_checkboxes_frame, text="Instagram",
                       variable=self.instagram_var).pack(side="left", padx=10)
        ctk.CTkCheckBox(platforms_checkboxes_frame, text="TikTok",
                       variable=self.tiktok_var).pack(side="left", padx=10)

        # إطار الوسائط
        media_frame = ctk.CTkFrame(content_frame)
        media_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(media_frame, text="الوسائط:", font=("Arial", 14, "bold")).pack(anchor="w", padx=5)

        media_buttons_frame = ctk.CTkFrame(media_frame)
        media_buttons_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkButton(media_buttons_frame, text="إضافة صور",
                     command=self.add_images).pack(side="left", padx=5)
        ctk.CTkButton(media_buttons_frame, text="إضافة فيديو",
                     command=self.add_video).pack(side="left", padx=5)
        ctk.CTkButton(media_buttons_frame, text="مسح الكل",
                     command=self.clear_media).pack(side="left", padx=5)

        # قائمة الملفات المحددة
        self.media_listbox = tk.Listbox(media_frame, height=4)
        self.media_listbox.pack(fill="x", padx=10, pady=5)

        # إطار الجدولة
        schedule_frame = ctk.CTkFrame(content_frame)
        schedule_frame.pack(fill="x", padx=10, pady=5)

        self.schedule_var = tk.BooleanVar()
        ctk.CTkCheckBox(schedule_frame, text="جدولة المنشور",
                       variable=self.schedule_var, command=self.toggle_schedule).pack(anchor="w", padx=5)

        self.schedule_datetime_frame = ctk.CTkFrame(schedule_frame)

        # أزرار العمل
        action_frame = ctk.CTkFrame(content_frame)
        action_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkButton(action_frame, text="نشر الآن", command=self.post_now,
                     font=("Arial", 14, "bold"), height=40).pack(side="left", padx=10)
        ctk.CTkButton(action_frame, text="حفظ كمسودة", command=self.save_draft,
                     height=40).pack(side="left", padx=10)
        ctk.CTkButton(action_frame, text="معاينة", command=self.preview_post,
                     height=40).pack(side="left", padx=10)

    def create_schedule_tab(self):
        """إنشاء تبويب الجدولة"""
        schedule_tab = self.notebook.add("الجدولة")

        # قائمة المنشورات المجدولة
        ctk.CTkLabel(schedule_tab, text="المنشورات المجدولة",
                    font=("Arial", 16, "bold")).pack(pady=10)

        # جدول المنشورات المجدولة
        columns = ("العنوان", "المنصات", "الوقت المجدول", "الحالة")
        self.schedule_tree = ttk.Treeview(schedule_tab, columns=columns, show="headings", height=15)

        for col in columns:
            self.schedule_tree.heading(col, text=col)
            self.schedule_tree.column(col, width=200)

        self.schedule_tree.pack(fill="both", expand=True, padx=10, pady=5)

        # أزرار إدارة الجدولة
        schedule_buttons_frame = ctk.CTkFrame(schedule_tab)
        schedule_buttons_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkButton(schedule_buttons_frame, text="تحديث القائمة",
                     command=self.refresh_schedule_list).pack(side="left", padx=5)
        ctk.CTkButton(schedule_buttons_frame, text="حذف المحدد",
                     command=self.delete_scheduled_post).pack(side="left", padx=5)
        ctk.CTkButton(schedule_buttons_frame, text="تعديل الوقت",
                     command=self.edit_schedule_time).pack(side="left", padx=5)

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        settings_tab = self.notebook.add("الإعدادات")

        # إعدادات حدود المعدل
        rate_limit_frame = ctk.CTkFrame(settings_tab)
        rate_limit_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(rate_limit_frame, text="إعدادات حدود المعدل",
                    font=("Arial", 16, "bold")).pack(pady=5)

        # تفعيل تخطي الحدود
        self.bypass_var = tk.BooleanVar(value=rate_limiter.bypass_settings['enabled'])
        ctk.CTkCheckBox(rate_limit_frame, text="تفعيل تخطي حدود المنصات",
                       variable=self.bypass_var, command=self.toggle_bypass).pack(anchor="w", padx=10)

        # تأخير تخطي الحدود
        delay_frame = ctk.CTkFrame(rate_limit_frame)
        delay_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(delay_frame, text="التأخير بين المنشورات (ثانية):", width=200).pack(side="left", padx=5)
        self.delay_var = tk.StringVar(value=str(rate_limiter.bypass_settings['delay_between_posts']))
        delay_entry = ctk.CTkEntry(delay_frame, textvariable=self.delay_var, width=100)
        delay_entry.pack(side="left", padx=5)
        ctk.CTkButton(delay_frame, text="تطبيق", command=self.apply_delay_setting,
                     width=80).pack(side="left", padx=5)

        # إعدادات الهاشتاجات الافتراضية
        hashtags_frame = ctk.CTkFrame(settings_tab)
        hashtags_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(hashtags_frame, text="الهاشتاجات الافتراضية",
                    font=("Arial", 16, "bold")).pack(pady=5)

        # هاشتاجات Facebook
        fb_hashtags_frame = ctk.CTkFrame(hashtags_frame)
        fb_hashtags_frame.pack(fill="x", padx=10, pady=2)

        ctk.CTkLabel(fb_hashtags_frame, text="Facebook:", width=100).pack(side="left", padx=5)
        self.fb_hashtags_var = tk.StringVar(value=config.get('default_hashtags.facebook', ''))
        ctk.CTkEntry(fb_hashtags_frame, textvariable=self.fb_hashtags_var).pack(side="left", padx=5, fill="x", expand=True)

        # هاشتاجات Instagram
        ig_hashtags_frame = ctk.CTkFrame(hashtags_frame)
        ig_hashtags_frame.pack(fill="x", padx=10, pady=2)

        ctk.CTkLabel(ig_hashtags_frame, text="Instagram:", width=100).pack(side="left", padx=5)
        self.ig_hashtags_var = tk.StringVar(value=config.get('default_hashtags.instagram', ''))
        ctk.CTkEntry(ig_hashtags_frame, textvariable=self.ig_hashtags_var).pack(side="left", padx=5, fill="x", expand=True)

        # هاشتاجات TikTok
        tt_hashtags_frame = ctk.CTkFrame(hashtags_frame)
        tt_hashtags_frame.pack(fill="x", padx=10, pady=2)

        ctk.CTkLabel(tt_hashtags_frame, text="TikTok:", width=100).pack(side="left", padx=5)
        self.tt_hashtags_var = tk.StringVar(value=config.get('default_hashtags.tiktok', ''))
        ctk.CTkEntry(tt_hashtags_frame, textvariable=self.tt_hashtags_var).pack(side="left", padx=5, fill="x", expand=True)

        # أزرار الإعدادات
        settings_buttons_frame = ctk.CTkFrame(settings_tab)
        settings_buttons_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkButton(settings_buttons_frame, text="حفظ الإعدادات",
                     command=self.save_settings).pack(side="left", padx=5)
        ctk.CTkButton(settings_buttons_frame, text="استعادة الافتراضية",
                     command=self.reset_settings).pack(side="left", padx=5)

    def create_stats_tab(self):
        """إنشاء تبويب الإحصائيات"""
        stats_tab = self.notebook.add("الإحصائيات")

        # إحصائيات حدود المعدل
        rate_stats_frame = ctk.CTkFrame(stats_tab)
        rate_stats_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(rate_stats_frame, text="إحصائيات حدود المعدل",
                    font=("Arial", 16, "bold")).pack(pady=5)

        # إطار إحصائيات المنصات
        self.stats_frame = ctk.CTkFrame(rate_stats_frame)
        self.stats_frame.pack(fill="x", padx=10, pady=5)

        # زر تحديث الإحصائيات
        ctk.CTkButton(rate_stats_frame, text="تحديث الإحصائيات",
                     command=self.update_stats).pack(pady=10)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = ctk.CTkFrame(self.root, height=30)
        self.status_frame.pack(fill="x", side="bottom", padx=10, pady=5)

        self.status_label = ctk.CTkLabel(self.status_frame, text="جاهز")
        self.status_label.pack(side="left", padx=10, pady=5)

        # مؤشر الاتصال
        self.connection_label = ctk.CTkLabel(self.status_frame, text="غير متصل",
                                           text_color="red")
        self.connection_label.pack(side="right", padx=10, pady=5)

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            # تحميل بيانات API
            if self.api_key_var.get():
                ayrshare_client.set_credentials(self.api_key_var.get(), self.profile_key_var.get())
                self.test_api_connection()

            # تحديث الإحصائيات
            self.update_stats()

            logger.info("تم تحميل الإعدادات")

        except Exception as e:
            logger.error(f"خطأ في تحميل الإعدادات: {e}")

    def test_api_connection(self):
        """اختبار اتصال API"""
        def test_in_thread():
            try:
                self.update_status("جاري اختبار الاتصال...")

                # تعيين بيانات الاعتماد
                ayrshare_client.set_credentials(self.api_key_var.get(), self.profile_key_var.get())

                # اختبار الاتصال
                success, message = ayrshare_client.test_connection()

                if success:
                    self.connection_label.configure(text="متصل", text_color="green")
                    self.update_status("تم الاتصال بنجاح")
                    messagebox.showinfo("نجح الاتصال", "تم الاتصال بـ Ayrshare API بنجاح!")
                else:
                    self.connection_label.configure(text="فشل الاتصال", text_color="red")
                    self.update_status(f"فشل الاتصال: {message}")
                    messagebox.showerror("فشل الاتصال", f"فشل في الاتصال: {message}")

            except Exception as e:
                self.connection_label.configure(text="خطأ", text_color="red")
                self.update_status(f"خطأ في الاتصال: {e}")
                messagebox.showerror("خطأ", f"خطأ في اختبار الاتصال: {e}")

        threading.Thread(target=test_in_thread, daemon=True).start()

    def add_images(self):
        """إضافة صور"""
        try:
            file_types = [
                ("ملفات الصور", "*.jpg *.jpeg *.png *.gif *.bmp"),
                ("جميع الملفات", "*.*")
            ]

            files = filedialog.askopenfilenames(
                title="اختر الصور",
                filetypes=file_types
            )

            for file_path in files:
                if file_path not in self.selected_media_files:
                    self.selected_media_files.append(file_path)
                    self.media_listbox.insert(tk.END, os.path.basename(file_path))

            self.update_status(f"تم إضافة {len(files)} صورة")

        except Exception as e:
            logger.error(f"خطأ في إضافة الصور: {e}")
            messagebox.showerror("خطأ", f"خطأ في إضافة الصور: {e}")

    def add_video(self):
        """إضافة فيديو"""
        try:
            file_types = [
                ("ملفات الفيديو", "*.mp4 *.mov *.avi *.mkv *.wmv"),
                ("جميع الملفات", "*.*")
            ]

            file_path = filedialog.askopenfilename(
                title="اختر فيديو",
                filetypes=file_types
            )

            if file_path and file_path not in self.selected_media_files:
                self.selected_media_files.append(file_path)
                self.media_listbox.insert(tk.END, os.path.basename(file_path))
                self.update_status("تم إضافة الفيديو")

        except Exception as e:
            logger.error(f"خطأ في إضافة الفيديو: {e}")
            messagebox.showerror("خطأ", f"خطأ في إضافة الفيديو: {e}")

    def clear_media(self):
        """مسح جميع الملفات المحددة"""
        self.selected_media_files.clear()
        self.media_listbox.delete(0, tk.END)
        self.update_status("تم مسح جميع الملفات")

    def toggle_schedule(self):
        """تبديل عرض إعدادات الجدولة"""
        if self.schedule_var.get():
            self.schedule_datetime_frame.pack(fill="x", padx=10, pady=5)
            self.create_schedule_widgets()
        else:
            self.schedule_datetime_frame.pack_forget()

    def create_schedule_widgets(self):
        """إنشاء عناصر الجدولة"""
        # مسح العناصر الموجودة
        for widget in self.schedule_datetime_frame.winfo_children():
            widget.destroy()

        # التاريخ والوقت
        datetime_frame = ctk.CTkFrame(self.schedule_datetime_frame)
        datetime_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(datetime_frame, text="التاريخ والوقت:", width=100).pack(side="left", padx=5)

        # التاريخ
        self.date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        date_entry = ctk.CTkEntry(datetime_frame, textvariable=self.date_var, width=120)
        date_entry.pack(side="left", padx=5)

        # الوقت
        self.time_var = tk.StringVar(value=datetime.now().strftime("%H:%M"))
        time_entry = ctk.CTkEntry(datetime_frame, textvariable=self.time_var, width=80)
        time_entry.pack(side="left", padx=5)

    def get_selected_platforms(self) -> List[str]:
        """الحصول على المنصات المحددة"""
        platforms = []
        if self.facebook_var.get():
            platforms.append("facebook")
        if self.instagram_var.get():
            platforms.append("instagram")
        if self.tiktok_var.get():
            platforms.append("tiktok")
        return platforms

    def post_now(self):
        """نشر المحتوى الآن"""
        def post_in_thread():
            try:
                self.update_status("جاري النشر...")

                # التحقق من البيانات
                content = self.content_text.get("1.0", tk.END).strip()
                if not content:
                    messagebox.showerror("خطأ", "يرجى إدخال محتوى المنشور")
                    return

                platforms = self.get_selected_platforms()
                if not platforms:
                    messagebox.showerror("خطأ", "يرجى اختيار منصة واحدة على الأقل")
                    return

                hashtags = self.hashtags_var.get().strip()

                # رفع الملفات إذا كانت موجودة
                media_urls = []
                if self.selected_media_files:
                    self.update_status("جاري رفع الملفات...")
                    for file_path in self.selected_media_files:
                        upload_result = ayrshare_client.upload_media(file_path)
                        if upload_result.get('status') == 'success':
                            media_urls.append(upload_result.get('url'))
                        else:
                            messagebox.showerror("خطأ", f"فشل رفع الملف: {upload_result.get('message')}")
                            return

                # النشر
                result = ayrshare_client.post_content(
                    content=content,
                    platforms=platforms,
                    media_urls=media_urls if media_urls else None,
                    hashtags=hashtags if hashtags else None
                )

                if result.get('status') == 'success':
                    # حفظ في قاعدة البيانات
                    post = Post(
                        title=content[:50] + "..." if len(content) > 50 else content,
                        content=content,
                        media_paths=self.selected_media_files,
                        platforms=platforms,
                        hashtags=hashtags,
                        status="published",
                        published_at=datetime.now(),
                        ayrshare_id=result.get('id')
                    )
                    db.create_post(post)

                    self.update_status("تم النشر بنجاح!")
                    messagebox.showinfo("نجح النشر", "تم نشر المحتوى بنجاح!")

                    # مسح النموذج
                    self.clear_form()

                else:
                    error_msg = result.get('message', 'خطأ غير معروف')
                    self.update_status(f"فشل النشر: {error_msg}")
                    messagebox.showerror("فشل النشر", f"فشل في النشر: {error_msg}")

            except Exception as e:
                logger.error(f"خطأ في النشر: {e}")
                self.update_status(f"خطأ في النشر: {e}")
                messagebox.showerror("خطأ", f"خطأ في النشر: {e}")

        threading.Thread(target=post_in_thread, daemon=True).start()

    def save_draft(self):
        """حفظ كمسودة"""
        try:
            content = self.content_text.get("1.0", tk.END).strip()
            if not content:
                messagebox.showerror("خطأ", "يرجى إدخال محتوى المنشور")
                return

            platforms = self.get_selected_platforms()
            hashtags = self.hashtags_var.get().strip()

            # إنشاء منشور مسودة
            post = Post(
                title=content[:50] + "..." if len(content) > 50 else content,
                content=content,
                media_paths=self.selected_media_files.copy(),
                platforms=platforms,
                hashtags=hashtags,
                status="draft"
            )

            post_id = db.create_post(post)
            if post_id:
                self.update_status("تم حفظ المسودة")
                messagebox.showinfo("تم الحفظ", "تم حفظ المنشور كمسودة")
                self.clear_form()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ المسودة")

        except Exception as e:
            logger.error(f"خطأ في حفظ المسودة: {e}")
            messagebox.showerror("خطأ", f"خطأ في حفظ المسودة: {e}")

    def preview_post(self):
        """معاينة المنشور"""
        try:
            content = self.content_text.get("1.0", tk.END).strip()
            hashtags = self.hashtags_var.get().strip()
            platforms = self.get_selected_platforms()

            preview_text = f"المحتوى:\n{content}\n\n"
            if hashtags:
                preview_text += f"الهاشتاجات:\n{hashtags}\n\n"
            preview_text += f"المنصات:\n{', '.join(platforms)}\n\n"
            if self.selected_media_files:
                preview_text += f"الملفات:\n" + "\n".join([os.path.basename(f) for f in self.selected_media_files])

            messagebox.showinfo("معاينة المنشور", preview_text)

        except Exception as e:
            logger.error(f"خطأ في المعاينة: {e}")
            messagebox.showerror("خطأ", f"خطأ في المعاينة: {e}")

    def clear_form(self):
        """مسح النموذج"""
        self.content_text.delete("1.0", tk.END)
        self.hashtags_var.set("")
        self.clear_media()
        self.schedule_var.set(False)
        self.toggle_schedule()

    def update_status(self, message: str):
        """تحديث شريط الحالة"""
        self.status_label.configure(text=message)
        self.root.update_idletasks()

    # ===== وظائف الجدولة =====

    def refresh_schedule_list(self):
        """تحديث قائمة المنشورات المجدولة"""
        try:
            # مسح القائمة الحالية
            for item in self.schedule_tree.get_children():
                self.schedule_tree.delete(item)

            # الحصول على المنشورات المجدولة
            scheduled_posts = db.get_scheduled_posts()

            for scheduled_post in scheduled_posts:
                # الحصول على بيانات المنشور
                post = db.get_post(scheduled_post.post_id)
                if post:
                    platforms_str = ", ".join(post.platforms)
                    scheduled_time_str = scheduled_post.scheduled_time.strftime("%Y-%m-%d %H:%M")
                    status = "نشط" if scheduled_post.is_active else "معطل"

                    self.schedule_tree.insert("", "end", values=(
                        post.title,
                        platforms_str,
                        scheduled_time_str,
                        status
                    ))

            self.update_status(f"تم تحديث قائمة الجدولة - {len(scheduled_posts)} منشور")

        except Exception as e:
            logger.error(f"خطأ في تحديث قائمة الجدولة: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحديث القائمة: {e}")

    def delete_scheduled_post(self):
        """حذف منشور مجدول"""
        try:
            selected_item = self.schedule_tree.selection()
            if not selected_item:
                messagebox.showwarning("تحذير", "يرجى اختيار منشور لحذفه")
                return

            if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المنشور المجدول؟"):
                # هنا يمكن إضافة منطق الحذف
                self.refresh_schedule_list()
                self.update_status("تم حذف المنشور المجدول")

        except Exception as e:
            logger.error(f"خطأ في حذف المنشور المجدول: {e}")
            messagebox.showerror("خطأ", f"خطأ في الحذف: {e}")

    def edit_schedule_time(self):
        """تعديل وقت الجدولة"""
        try:
            selected_item = self.schedule_tree.selection()
            if not selected_item:
                messagebox.showwarning("تحذير", "يرجى اختيار منشور لتعديل وقته")
                return

            # هنا يمكن إضافة نافذة تعديل الوقت
            messagebox.showinfo("قريباً", "ميزة تعديل الوقت ستكون متاحة قريباً")

        except Exception as e:
            logger.error(f"خطأ في تعديل وقت الجدولة: {e}")
            messagebox.showerror("خطأ", f"خطأ في التعديل: {e}")

    # ===== وظائف الإعدادات =====

    def toggle_bypass(self):
        """تبديل تفعيل تخطي الحدود"""
        try:
            enabled = self.bypass_var.get()
            rate_limiter.enable_bypass(enabled)
            status = "مفعل" if enabled else "معطل"
            self.update_status(f"تخطي الحدود: {status}")

        except Exception as e:
            logger.error(f"خطأ في تبديل تخطي الحدود: {e}")
            messagebox.showerror("خطأ", f"خطأ في تبديل الإعداد: {e}")

    def apply_delay_setting(self):
        """تطبيق إعداد التأخير"""
        try:
            delay = int(self.delay_var.get())
            if delay < 0:
                messagebox.showerror("خطأ", "التأخير يجب أن يكون رقماً موجباً")
                return

            rate_limiter.set_bypass_delay(delay)
            self.update_status(f"تم تعيين التأخير: {delay} ثانية")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح للتأخير")
        except Exception as e:
            logger.error(f"خطأ في تطبيق إعداد التأخير: {e}")
            messagebox.showerror("خطأ", f"خطأ في تطبيق الإعداد: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ بيانات API
            config.set('api_key', self.api_key_var.get())
            config.set('profile_key', self.profile_key_var.get())

            # حفظ الهاشتاجات الافتراضية
            config.set('default_hashtags.facebook', self.fb_hashtags_var.get())
            config.set('default_hashtags.instagram', self.ig_hashtags_var.get())
            config.set('default_hashtags.tiktok', self.tt_hashtags_var.get())

            self.update_status("تم حفظ الإعدادات")
            messagebox.showinfo("تم الحفظ", "تم حفظ جميع الإعدادات بنجاح")

        except Exception as e:
            logger.error(f"خطأ في حفظ الإعدادات: {e}")
            messagebox.showerror("خطأ", f"خطأ في حفظ الإعدادات: {e}")

    def reset_settings(self):
        """استعادة الإعدادات الافتراضية"""
        try:
            if messagebox.askyesno("تأكيد الاستعادة", "هل أنت متأكد من استعادة الإعدادات الافتراضية؟"):
                config.reset_to_default()

                # تحديث الواجهة
                self.api_key_var.set("")
                self.profile_key_var.set("")
                self.fb_hashtags_var.set(config.get('default_hashtags.facebook', ''))
                self.ig_hashtags_var.set(config.get('default_hashtags.instagram', ''))
                self.tt_hashtags_var.set(config.get('default_hashtags.tiktok', ''))
                self.bypass_var.set(False)
                self.delay_var.set("60")

                self.update_status("تم استعادة الإعدادات الافتراضية")
                messagebox.showinfo("تم الاستعادة", "تم استعادة الإعدادات الافتراضية")

        except Exception as e:
            logger.error(f"خطأ في استعادة الإعدادات: {e}")
            messagebox.showerror("خطأ", f"خطأ في الاستعادة: {e}")

    # ===== وظائف الإحصائيات =====

    def update_stats(self):
        """تحديث الإحصائيات"""
        try:
            # مسح الإحصائيات الحالية
            for widget in self.stats_frame.winfo_children():
                widget.destroy()

            platforms = ['facebook', 'instagram', 'tiktok']

            for platform in platforms:
                stats = rate_limiter.get_usage_stats(platform)
                if stats:
                    # إطار إحصائيات المنصة
                    platform_frame = ctk.CTkFrame(self.stats_frame)
                    platform_frame.pack(fill="x", padx=5, pady=5)

                    # عنوان المنصة
                    platform_name = {
                        'facebook': 'Facebook',
                        'instagram': 'Instagram',
                        'tiktok': 'TikTok'
                    }.get(platform, platform)

                    ctk.CTkLabel(platform_frame, text=platform_name,
                               font=("Arial", 14, "bold")).pack(anchor="w", padx=5, pady=2)

                    # الإحصائيات الساعية
                    hourly_text = f"الاستخدام الساعي: {stats.get('hourly_used', 0)}/{stats.get('hourly_limit', 0)}"
                    ctk.CTkLabel(platform_frame, text=hourly_text).pack(anchor="w", padx=10)

                    # الإحصائيات اليومية
                    daily_text = f"الاستخدام اليومي: {stats.get('daily_used', 0)}/{stats.get('daily_limit', 0)}"
                    ctk.CTkLabel(platform_frame, text=daily_text).pack(anchor="w", padx=10)

                    # إحصائيات خاصة
                    if platform == 'facebook' and 'reels_daily_used' in stats:
                        reels_text = f"الريلز اليومي: {stats.get('reels_daily_used', 0)}/{stats.get('reels_daily_limit', 0)}"
                        ctk.CTkLabel(platform_frame, text=reels_text).pack(anchor="w", padx=10)

                    elif platform == 'instagram' and 'stories_daily_used' in stats:
                        stories_text = f"الستوريز اليومي: {stats.get('stories_daily_used', 0)}/{stats.get('stories_daily_limit', 0)}"
                        ctk.CTkLabel(platform_frame, text=stories_text).pack(anchor="w", padx=10)

            self.update_status("تم تحديث الإحصائيات")

        except Exception as e:
            logger.error(f"خطأ في تحديث الإحصائيات: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحديث الإحصائيات: {e}")

    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.mainloop()
        except Exception as e:
            logger.error(f"خطأ في تشغيل التطبيق: {e}")
            messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    app = MainWindow()
    app.run()
