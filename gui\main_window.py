# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from typing import Optional, List
import threading
from datetime import datetime, timedelta
import os

from utils.config import config
from utils.logger import logger
from utils.notifications import notification_manager
from utils.backup import backup_manager
from utils.updater import update_manager
from utils.analytics import analytics_manager
from api.simple_ayrshare_client import ayrshare_client
from api.rate_limiter import rate_limiter
from database.database import db
from database.models import Post
from gui.advanced_settings import AdvancedSettingsWindow

# إعداد CustomTkinter
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class MainWindow:
    """النافذة الرئيسية للتطبيق"""

    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Ayrshare Social Media Manager")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # متغيرات الواجهة
        self.api_key_var = tk.StringVar(value=config.get('api_key', ''))
        self.profile_key_var = tk.StringVar(value=config.get('profile_key', ''))
        self.content_var = tk.StringVar()
        self.hashtags_var = tk.StringVar()

        # قوائم المنصات والملفات
        self.selected_platforms = []
        self.selected_media_files = []

        # إعداد الواجهة
        self.setup_ui()
        self.load_settings()

        logger.info("تم تشغيل التطبيق")

    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        try:
            # إنشاء شريط القوائم
            menubar = tk.Menu(self.root)
            self.root.config(menu=menubar)

            # قائمة الملف
            file_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="ملف", menu=file_menu)
            file_menu.add_command(label="نسخة احتياطية جديدة", command=self.create_backup)
            file_menu.add_command(label="استعادة نسخة احتياطية", command=self.restore_backup)
            file_menu.add_separator()
            file_menu.add_command(label="تصدير الإعدادات", command=self.export_settings)
            file_menu.add_command(label="استيراد الإعدادات", command=self.import_settings)
            file_menu.add_separator()
            file_menu.add_command(label="خروج", command=self.root.quit)

            # قائمة الأدوات
            tools_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="أدوات", menu=tools_menu)
            tools_menu.add_command(label="الإعدادات المتقدمة", command=self.open_advanced_settings)
            tools_menu.add_command(label="تقرير الإحصائيات", command=self.show_analytics_report)
            tools_menu.add_command(label="فحص التحديثات", command=self.check_updates)
            tools_menu.add_separator()
            tools_menu.add_command(label="مسح ذاكرة التخزين المؤقت", command=self.clear_cache)
            tools_menu.add_command(label="إعادة تشغيل", command=self.restart_app)

            # قائمة المساعدة
            help_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="مساعدة", menu=help_menu)
            help_menu.add_command(label="دليل الاستخدام", command=self.show_help)
            help_menu.add_command(label="الأسئلة الشائعة", command=self.show_faq)
            help_menu.add_command(label="اختبار الإشعارات", command=self.test_notifications)
            help_menu.add_separator()
            help_menu.add_command(label="حول البرنامج", command=self.show_about)

        except Exception as e:
            logger.error(f"خطأ في إنشاء شريط القوائم: {e}")

    def start_background_services(self):
        """بدء الخدمات في الخلفية"""
        try:
            # بدء النسخ الاحتياطي التلقائي
            backup_manager.start_auto_backup()

            # فحص التحديثات إذا كان مفعلاً
            if update_manager.should_check_for_updates():
                threading.Thread(target=self.auto_check_updates, daemon=True).start()

            logger.info("تم بدء الخدمات الخلفية")

        except Exception as e:
            logger.error(f"خطأ في بدء الخدمات الخلفية: {e}")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # إنشاء شريط القوائم
        self.create_menu_bar()

        # إنشاء التبويبات
        self.notebook = ctk.CTkTabview(main_frame)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # تبويب النشر
        self.create_post_tab()

        # تبويب الجدولة
        self.create_schedule_tab()

        # تبويب الإعدادات
        self.create_settings_tab()

        # تبويب الإحصائيات
        self.create_stats_tab()

        # شريط الحالة
        self.create_status_bar()

        # بدء الخدمات في الخلفية
        self.start_background_services()

    def create_post_tab(self):
        """إنشاء تبويب النشر"""
        post_tab = self.notebook.add("نشر محتوى")

        # إنشاء إطار قابل للتمرير للنشر
        post_scrollable = ctk.CTkScrollableFrame(post_tab)
        post_scrollable.pack(fill="both", expand=True, padx=10, pady=10)

        # إطار إعدادات API
        api_frame = ctk.CTkFrame(post_scrollable)
        api_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(api_frame, text="إعدادات API", font=("Arial", 16, "bold")).pack(pady=5)

        # API Key
        api_key_frame = ctk.CTkFrame(api_frame)
        api_key_frame.pack(fill="x", padx=10, pady=2)

        ctk.CTkLabel(api_key_frame, text="API Key:", width=100).pack(side="left", padx=5)
        self.api_key_entry = ctk.CTkEntry(api_key_frame, textvariable=self.api_key_var,
                                         show="*", width=300)
        self.api_key_entry.pack(side="left", padx=5, fill="x", expand=True)

        ctk.CTkButton(api_key_frame, text="اختبار", command=self.test_api_connection,
                     width=80).pack(side="right", padx=5)

        # Profile Key
        profile_frame = ctk.CTkFrame(api_frame)
        profile_frame.pack(fill="x", padx=10, pady=2)

        ctk.CTkLabel(profile_frame, text="Profile Key:", width=100).pack(side="left", padx=5)
        self.profile_key_entry = ctk.CTkEntry(profile_frame, textvariable=self.profile_key_var,
                                             width=300)
        self.profile_key_entry.pack(side="left", padx=5, fill="x", expand=True)

        # إطار المحتوى
        content_frame = ctk.CTkFrame(post_scrollable)
        content_frame.pack(fill="both", expand=True, padx=10, pady=5)

        ctk.CTkLabel(content_frame, text="محتوى المنشور", font=("Arial", 16, "bold")).pack(pady=5)

        # نص المحتوى مع دعم النسخ واللصق
        self.content_text = ctk.CTkTextbox(content_frame, height=150)
        self.content_text.pack(fill="x", padx=10, pady=5)

        # ربط اختصارات النسخ واللصق
        self.content_text.bind("<Control-c>", self.copy_text)
        self.content_text.bind("<Control-v>", self.paste_text)
        self.content_text.bind("<Control-a>", self.select_all_text)
        self.content_text.bind("<Control-x>", self.cut_text)
        self.content_text.bind("<Button-3>", self.show_context_menu)

        # الهاشتاجات
        hashtags_frame = ctk.CTkFrame(content_frame)
        hashtags_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(hashtags_frame, text="الهاشتاجات:", width=100).pack(side="left", padx=5)
        self.hashtags_entry = ctk.CTkEntry(hashtags_frame, textvariable=self.hashtags_var)
        self.hashtags_entry.pack(side="left", padx=5, fill="x", expand=True)

        # ربط اختصارات النسخ واللصق للهاشتاجات
        self.hashtags_entry.bind("<Control-c>", self.copy_hashtags)
        self.hashtags_entry.bind("<Control-v>", self.paste_hashtags)
        self.hashtags_entry.bind("<Control-a>", self.select_all_hashtags)
        self.hashtags_entry.bind("<Control-x>", self.cut_hashtags)

        # ربط اختصارات النسخ واللصق لحقل API Key
        self.api_key_entry.bind("<Control-c>", self.copy_api_key)
        self.api_key_entry.bind("<Control-v>", self.paste_api_key)
        self.api_key_entry.bind("<Control-a>", self.select_all_api_key)
        self.api_key_entry.bind("<Control-x>", self.cut_api_key)
        self.api_key_entry.bind("<Button-3>", self.show_api_key_context_menu)

        # ربط اختصارات النسخ واللصق لحقل Profile Key
        self.profile_key_entry.bind("<Control-c>", self.copy_profile_key)
        self.profile_key_entry.bind("<Control-v>", self.paste_profile_key)
        self.profile_key_entry.bind("<Control-a>", self.select_all_profile_key)
        self.profile_key_entry.bind("<Control-x>", self.cut_profile_key)

        # إطار المنصات
        platforms_frame = ctk.CTkFrame(content_frame)
        platforms_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(platforms_frame, text="المنصات:", font=("Arial", 14, "bold")).pack(anchor="w", padx=5)

        platforms_checkboxes_frame = ctk.CTkFrame(platforms_frame)
        platforms_checkboxes_frame.pack(fill="x", padx=10, pady=5)

        # خانات اختيار المنصات
        self.facebook_var = tk.BooleanVar(value=True)
        self.instagram_var = tk.BooleanVar(value=True)
        self.tiktok_var = tk.BooleanVar(value=True)
        self.twitter_var = tk.BooleanVar(value=True)

        ctk.CTkCheckBox(platforms_checkboxes_frame, text="📘 Facebook",
                       variable=self.facebook_var).pack(side="left", padx=10)
        ctk.CTkCheckBox(platforms_checkboxes_frame, text="📸 Instagram",
                       variable=self.instagram_var).pack(side="left", padx=10)
        ctk.CTkCheckBox(platforms_checkboxes_frame, text="🎵 TikTok",
                       variable=self.tiktok_var).pack(side="left", padx=10)
        ctk.CTkCheckBox(platforms_checkboxes_frame, text="🐦 Twitter/X",
                       variable=self.twitter_var).pack(side="left", padx=10)

        # إطار الوسائط
        media_frame = ctk.CTkFrame(content_frame)
        media_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(media_frame, text="الوسائط:", font=("Arial", 14, "bold")).pack(anchor="w", padx=5)

        media_buttons_frame = ctk.CTkFrame(media_frame)
        media_buttons_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkButton(media_buttons_frame, text="إضافة صور",
                     command=self.add_images).pack(side="left", padx=5)
        ctk.CTkButton(media_buttons_frame, text="إضافة فيديو",
                     command=self.add_video).pack(side="left", padx=5)
        ctk.CTkButton(media_buttons_frame, text="مسح الكل",
                     command=self.clear_media).pack(side="left", padx=5)

        # قائمة الملفات المحددة
        self.media_listbox = tk.Listbox(media_frame, height=4)
        self.media_listbox.pack(fill="x", padx=10, pady=5)

        # إطار الجدولة
        schedule_frame = ctk.CTkFrame(content_frame)
        schedule_frame.pack(fill="x", padx=10, pady=5)

        self.schedule_var = tk.BooleanVar()
        ctk.CTkCheckBox(schedule_frame, text="جدولة المنشور",
                       variable=self.schedule_var, command=self.toggle_schedule).pack(anchor="w", padx=5)

        self.schedule_datetime_frame = ctk.CTkFrame(schedule_frame)

        # أزرار العمل
        action_frame = ctk.CTkFrame(content_frame)
        action_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkButton(action_frame, text="نشر الآن", command=self.post_now,
                     font=("Arial", 14, "bold"), height=40).pack(side="left", padx=10)
        ctk.CTkButton(action_frame, text="حفظ كمسودة", command=self.save_draft,
                     height=40).pack(side="left", padx=10)
        ctk.CTkButton(action_frame, text="معاينة", command=self.preview_post,
                     height=40).pack(side="left", padx=10)

    def create_schedule_tab(self):
        """إنشاء تبويب الجدولة"""
        schedule_tab = self.notebook.add("الجدولة")

        # إنشاء إطار قابل للتمرير للجدولة
        schedule_scrollable = ctk.CTkScrollableFrame(schedule_tab)
        schedule_scrollable.pack(fill="both", expand=True, padx=10, pady=10)

        # قائمة المنشورات المجدولة
        ctk.CTkLabel(schedule_scrollable, text="المنشورات المجدولة",
                    font=("Arial", 16, "bold")).pack(pady=10)

        # جدول المنشورات المجدولة
        columns = ("العنوان", "المنصات", "الوقت المجدول", "الحالة")
        self.schedule_tree = ttk.Treeview(schedule_scrollable, columns=columns, show="headings", height=15)

        for col in columns:
            self.schedule_tree.heading(col, text=col)
            self.schedule_tree.column(col, width=200)

        self.schedule_tree.pack(fill="both", expand=True, padx=10, pady=5)

        # أزرار إدارة الجدولة
        schedule_buttons_frame = ctk.CTkFrame(schedule_scrollable)
        schedule_buttons_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkButton(schedule_buttons_frame, text="تحديث القائمة",
                     command=self.refresh_schedule_list).pack(side="left", padx=5)
        ctk.CTkButton(schedule_buttons_frame, text="حذف المحدد",
                     command=self.delete_scheduled_post).pack(side="left", padx=5)
        ctk.CTkButton(schedule_buttons_frame, text="تعديل الوقت",
                     command=self.edit_schedule_time).pack(side="left", padx=5)

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        settings_tab = self.notebook.add("الإعدادات")

        # إنشاء إطار قابل للتمرير
        settings_scrollable = ctk.CTkScrollableFrame(settings_tab)
        settings_scrollable.pack(fill="both", expand=True, padx=10, pady=10)

        # إعدادات حدود المعدل
        rate_limit_frame = ctk.CTkFrame(settings_scrollable)
        rate_limit_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(rate_limit_frame, text="إعدادات حدود المعدل",
                    font=("Arial", 16, "bold")).pack(pady=5)

        # تفعيل تخطي الحدود
        self.bypass_var = tk.BooleanVar(value=rate_limiter.bypass_settings['enabled'])
        ctk.CTkCheckBox(rate_limit_frame, text="تفعيل تخطي حدود المنصات",
                       variable=self.bypass_var, command=self.toggle_bypass).pack(anchor="w", padx=10)

        # تأخير تخطي الحدود
        delay_frame = ctk.CTkFrame(rate_limit_frame)
        delay_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(delay_frame, text="التأخير بين المنشورات (ثانية):", width=200).pack(side="left", padx=5)
        self.delay_var = tk.StringVar(value=str(rate_limiter.bypass_settings['delay_between_posts']))
        delay_entry = ctk.CTkEntry(delay_frame, textvariable=self.delay_var, width=100)
        delay_entry.pack(side="left", padx=5)
        ctk.CTkButton(delay_frame, text="تطبيق", command=self.apply_delay_setting,
                     width=80).pack(side="left", padx=5)

        # إعدادات الهاشتاجات الافتراضية
        hashtags_frame = ctk.CTkFrame(settings_scrollable)
        hashtags_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(hashtags_frame, text="الهاشتاجات الافتراضية",
                    font=("Arial", 16, "bold")).pack(pady=5)

        # هاشتاجات Facebook
        fb_hashtags_frame = ctk.CTkFrame(hashtags_frame)
        fb_hashtags_frame.pack(fill="x", padx=10, pady=2)

        ctk.CTkLabel(fb_hashtags_frame, text="Facebook:", width=100).pack(side="left", padx=5)
        self.fb_hashtags_var = tk.StringVar(value=config.get('default_hashtags.facebook', ''))
        ctk.CTkEntry(fb_hashtags_frame, textvariable=self.fb_hashtags_var).pack(side="left", padx=5, fill="x", expand=True)

        # هاشتاجات Instagram
        ig_hashtags_frame = ctk.CTkFrame(hashtags_frame)
        ig_hashtags_frame.pack(fill="x", padx=10, pady=2)

        ctk.CTkLabel(ig_hashtags_frame, text="Instagram:", width=100).pack(side="left", padx=5)
        self.ig_hashtags_var = tk.StringVar(value=config.get('default_hashtags.instagram', ''))
        ctk.CTkEntry(ig_hashtags_frame, textvariable=self.ig_hashtags_var).pack(side="left", padx=5, fill="x", expand=True)

        # هاشتاجات TikTok
        tt_hashtags_frame = ctk.CTkFrame(hashtags_frame)
        tt_hashtags_frame.pack(fill="x", padx=10, pady=2)

        ctk.CTkLabel(tt_hashtags_frame, text="TikTok:", width=100).pack(side="left", padx=5)
        self.tt_hashtags_var = tk.StringVar(value=config.get('default_hashtags.tiktok', ''))
        ctk.CTkEntry(tt_hashtags_frame, textvariable=self.tt_hashtags_var).pack(side="left", padx=5, fill="x", expand=True)

        # هاشتاجات Twitter
        tw_hashtags_frame = ctk.CTkFrame(hashtags_frame)
        tw_hashtags_frame.pack(fill="x", padx=10, pady=2)

        ctk.CTkLabel(tw_hashtags_frame, text="Twitter:", width=100).pack(side="left", padx=5)
        self.tw_hashtags_var = tk.StringVar(value=config.get('default_hashtags.twitter', ''))
        ctk.CTkEntry(tw_hashtags_frame, textvariable=self.tw_hashtags_var).pack(side="left", padx=5, fill="x", expand=True)

        # أزرار الإعدادات
        settings_buttons_frame = ctk.CTkFrame(settings_scrollable)
        settings_buttons_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkButton(settings_buttons_frame, text="حفظ الإعدادات",
                     command=self.save_settings).pack(side="left", padx=5)
        ctk.CTkButton(settings_buttons_frame, text="استعادة الافتراضية",
                     command=self.reset_settings).pack(side="left", padx=5)

    def create_stats_tab(self):
        """إنشاء تبويب الإحصائيات"""
        stats_tab = self.notebook.add("الإحصائيات")

        # إنشاء إطار قابل للتمرير للإحصائيات
        stats_scrollable = ctk.CTkScrollableFrame(stats_tab)
        stats_scrollable.pack(fill="both", expand=True, padx=10, pady=10)

        # إحصائيات حدود المعدل
        rate_stats_frame = ctk.CTkFrame(stats_scrollable)
        rate_stats_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(rate_stats_frame, text="إحصائيات حدود المعدل",
                    font=("Arial", 16, "bold")).pack(pady=5)

        # إطار إحصائيات المنصات
        self.stats_frame = ctk.CTkFrame(rate_stats_frame)
        self.stats_frame.pack(fill="x", padx=10, pady=5)

        # زر تحديث الإحصائيات
        ctk.CTkButton(rate_stats_frame, text="تحديث الإحصائيات",
                     command=self.update_stats).pack(pady=10)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = ctk.CTkFrame(self.root, height=30)
        self.status_frame.pack(fill="x", side="bottom", padx=10, pady=5)

        self.status_label = ctk.CTkLabel(self.status_frame, text="جاهز")
        self.status_label.pack(side="left", padx=10, pady=5)

        # مؤشر الاتصال
        self.connection_label = ctk.CTkLabel(self.status_frame, text="غير متصل",
                                           text_color="red")
        self.connection_label.pack(side="right", padx=10, pady=5)

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            # تحميل بيانات API بدون اختبار تلقائي
            if self.api_key_var.get():
                ayrshare_client.set_credentials(self.api_key_var.get(), self.profile_key_var.get())
                # لا نختبر الاتصال تلقائياً لتجنب رسائل الخطأ
                self.connection_label.configure(text="غير مختبر", text_color="orange")

            # تحديث الإحصائيات
            self.update_stats()

            # إعادة ربط اختصارات النسخ واللصق بعد تحميل الإعدادات
            self.rebind_copy_paste_shortcuts()

            logger.info("تم تحميل الإعدادات")

        except Exception as e:
            logger.error(f"خطأ في تحميل الإعدادات: {e}")

    def test_api_connection(self):
        """اختبار اتصال API"""
        def test_in_thread():
            try:
                self.update_status("جاري اختبار الاتصال...")

                # التحقق من وجود API Key
                api_key = self.api_key_var.get().strip()
                if not api_key:
                    self.connection_label.configure(text="API Key مطلوب", text_color="orange")
                    self.update_status("يرجى إدخال API Key")
                    self.rebind_copy_paste_shortcuts()
                    messagebox.showwarning("تحذير", "يرجى إدخال API Key صالح أولاً")
                    return

                # تعيين بيانات الاعتماد
                ayrshare_client.set_credentials(api_key, self.profile_key_var.get())

                # اختبار الاتصال
                success, message = ayrshare_client.test_connection()

                if success:
                    self.connection_label.configure(text="متصل", text_color="green")
                    self.update_status("تم الاتصال بنجاح")
                    # إعادة ربط اختصارات النسخ واللصق بعد نجاح الاتصال
                    self.rebind_copy_paste_shortcuts()
                    messagebox.showinfo("نجح الاتصال", "تم الاتصال بـ Ayrshare API بنجاح!")
                else:
                    self.connection_label.configure(text="فشل الاتصال", text_color="red")
                    self.update_status(f"فشل الاتصال: {message}")
                    # إعادة ربط اختصارات النسخ واللصق حتى لو فشل الاتصال
                    self.rebind_copy_paste_shortcuts()

                    # رسالة خطأ مفصلة حسب نوع الخطأ
                    if "API Key غير صالح" in message or "API Key غير مصرح" in message:
                        error_msg = """🔑 API Key غير صالح!

❌ المشكلة:
API Key الذي أدخلته غير صحيح أو منتهي الصلاحية

✅ الحل:
1. تأكد من نسخ API Key بالكامل (بدون مسافات)
2. تأكد أن API Key لم تنته صلاحيته
3. تأكد أن حسابك نشط في Ayrshare

🔗 للحصول على API Key صالح:
• اذهب إلى: https://app.ayrshare.com
• سجل دخول لحسابك
• اذهب لقسم "API Key" أو "Settings"
• انسخ API Key الجديد بالكامل
• الصقه في التطبيق باستخدام Ctrl+V

💡 نصيحة: استخدم النسخ واللصق لتجنب الأخطاء"""
                        messagebox.showerror("🔑 API Key غير صالح", error_msg)
                    elif "لا يمكن الاتصال بالإنترنت" in message:
                        error_msg = """🌐 مشكلة في الاتصال بالإنترنت!

❌ المشكلة:
لا يمكن الوصول إلى خوادم Ayrshare

✅ الحل:
1. تحقق من اتصالك بالإنترنت
2. تأكد أن الجدار الناري لا يحجب التطبيق
3. جرب إعادة تشغيل الراوتر
4. جرب مرة أخرى بعد قليل"""
                        messagebox.showerror("🌐 مشكلة في الإنترنت", error_msg)
                    elif "انتهت مهلة الاتصال" in message:
                        error_msg = """⏰ انتهت مهلة الاتصال!

❌ المشكلة:
الاتصال بطيء جداً أو متقطع

✅ الحل:
1. تحقق من سرعة الإنترنت
2. جرب مرة أخرى بعد قليل
3. تأكد من عدم وجود تطبيقات تستهلك الإنترنت"""
                        messagebox.showerror("⏰ انتهت مهلة الاتصال", error_msg)
                    elif "تجاوز حدود الطلبات" in message:
                        error_msg = """🚫 تجاوز حدود الطلبات!

❌ المشكلة:
تم إرسال طلبات كثيرة جداً في وقت قصير

✅ الحل:
1. انتظر 5-10 دقائق ثم جرب مرة أخرى
2. تجنب الضغط على "اختبار" عدة مرات متتالية
3. راجع حدود خطتك في Ayrshare"""
                        messagebox.showerror("🚫 تجاوز حدود الطلبات", error_msg)
                    elif "خطأ في خادم Ayrshare" in message:
                        error_msg = """🔧 مشكلة في خادم Ayrshare!

❌ المشكلة:
خطأ مؤقت في خوادم Ayrshare

✅ الحل:
1. جرب مرة أخرى بعد 5-10 دقائق
2. تحقق من حالة خدمة Ayrshare على موقعهم
3. إذا استمرت المشكلة، تواصل مع دعم Ayrshare"""
                        messagebox.showerror("🔧 مشكلة في الخادم", error_msg)
                    else:
                        error_msg = f"""❌ فشل في الاتصال!

تفاصيل الخطأ:
{message}

💡 نصائح:
1. تحقق من API Key
2. تحقق من الاتصال بالإنترنت
3. جرب مرة أخرى بعد قليل"""
                        messagebox.showerror("❌ فشل الاتصال", error_msg)

            except Exception as e:
                self.connection_label.configure(text="خطأ", text_color="red")
                self.update_status(f"خطأ في الاتصال: {e}")
                # إعادة ربط اختصارات النسخ واللصق حتى في حالة الخطأ
                self.rebind_copy_paste_shortcuts()
                messagebox.showerror("خطأ", f"خطأ في اختبار الاتصال:\n{e}")

        threading.Thread(target=test_in_thread, daemon=True).start()

    def rebind_copy_paste_shortcuts(self):
        """إعادة ربط اختصارات النسخ واللصق"""
        try:
            # إعادة ربط اختصارات مربع النص الرئيسي
            self.content_text.bind("<Control-c>", self.copy_text)
            self.content_text.bind("<Control-v>", self.paste_text)
            self.content_text.bind("<Control-a>", self.select_all_text)
            self.content_text.bind("<Control-x>", self.cut_text)
            self.content_text.bind("<Button-3>", self.show_context_menu)

            # إعادة ربط اختصارات حقل الهاشتاجات
            self.hashtags_entry.bind("<Control-c>", self.copy_hashtags)
            self.hashtags_entry.bind("<Control-v>", self.paste_hashtags)
            self.hashtags_entry.bind("<Control-a>", self.select_all_hashtags)
            self.hashtags_entry.bind("<Control-x>", self.cut_hashtags)

            # إعادة ربط اختصارات حقل API Key
            self.api_key_entry.bind("<Control-c>", self.copy_api_key)
            self.api_key_entry.bind("<Control-v>", self.paste_api_key)
            self.api_key_entry.bind("<Control-a>", self.select_all_api_key)
            self.api_key_entry.bind("<Control-x>", self.cut_api_key)
            self.api_key_entry.bind("<Button-3>", self.show_api_key_context_menu)

            # إعادة ربط اختصارات حقل Profile Key
            self.profile_key_entry.bind("<Control-c>", self.copy_profile_key)
            self.profile_key_entry.bind("<Control-v>", self.paste_profile_key)
            self.profile_key_entry.bind("<Control-a>", self.select_all_profile_key)
            self.profile_key_entry.bind("<Control-x>", self.cut_profile_key)

            logger.info("تم إعادة ربط اختصارات النسخ واللصق")

        except Exception as e:
            logger.error(f"خطأ في إعادة ربط اختصارات النسخ واللصق: {e}")

    def add_images(self):
        """إضافة صور"""
        try:
            file_types = [
                ("ملفات الصور", "*.jpg *.jpeg *.png *.gif *.bmp"),
                ("جميع الملفات", "*.*")
            ]

            files = filedialog.askopenfilenames(
                title="اختر الصور",
                filetypes=file_types
            )

            for file_path in files:
                if file_path not in self.selected_media_files:
                    self.selected_media_files.append(file_path)
                    self.media_listbox.insert(tk.END, os.path.basename(file_path))

            self.update_status(f"تم إضافة {len(files)} صورة")

        except Exception as e:
            logger.error(f"خطأ في إضافة الصور: {e}")
            messagebox.showerror("خطأ", f"خطأ في إضافة الصور: {e}")

    def add_video(self):
        """إضافة فيديو"""
        try:
            file_types = [
                ("ملفات الفيديو", "*.mp4 *.mov *.avi *.mkv *.wmv"),
                ("جميع الملفات", "*.*")
            ]

            file_path = filedialog.askopenfilename(
                title="اختر فيديو",
                filetypes=file_types
            )

            if file_path and file_path not in self.selected_media_files:
                self.selected_media_files.append(file_path)
                self.media_listbox.insert(tk.END, os.path.basename(file_path))
                self.update_status("تم إضافة الفيديو")

        except Exception as e:
            logger.error(f"خطأ في إضافة الفيديو: {e}")
            messagebox.showerror("خطأ", f"خطأ في إضافة الفيديو: {e}")

    def clear_media(self):
        """مسح جميع الملفات المحددة"""
        self.selected_media_files.clear()
        self.media_listbox.delete(0, tk.END)
        self.update_status("تم مسح جميع الملفات")

    def toggle_schedule(self):
        """تبديل عرض إعدادات الجدولة"""
        if self.schedule_var.get():
            self.schedule_datetime_frame.pack(fill="x", padx=10, pady=5)
            self.create_schedule_widgets()
        else:
            self.schedule_datetime_frame.pack_forget()

    def create_schedule_widgets(self):
        """إنشاء عناصر الجدولة"""
        # مسح العناصر الموجودة
        for widget in self.schedule_datetime_frame.winfo_children():
            widget.destroy()

        # التاريخ والوقت
        datetime_frame = ctk.CTkFrame(self.schedule_datetime_frame)
        datetime_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(datetime_frame, text="التاريخ والوقت:", width=100).pack(side="left", padx=5)

        # التاريخ
        self.date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        date_entry = ctk.CTkEntry(datetime_frame, textvariable=self.date_var, width=120)
        date_entry.pack(side="left", padx=5)

        # الوقت
        self.time_var = tk.StringVar(value=datetime.now().strftime("%H:%M"))
        time_entry = ctk.CTkEntry(datetime_frame, textvariable=self.time_var, width=80)
        time_entry.pack(side="left", padx=5)

    def get_selected_platforms(self) -> List[str]:
        """الحصول على المنصات المحددة"""
        platforms = []
        if self.facebook_var.get():
            platforms.append("facebook")
        if self.instagram_var.get():
            platforms.append("instagram")
        if self.tiktok_var.get():
            platforms.append("tiktok")
        if self.twitter_var.get():
            platforms.append("twitter")
        return platforms

    def post_now(self):
        """نشر المحتوى الآن"""
        def post_in_thread():
            try:
                self.update_status("جاري النشر...")

                # التحقق من البيانات
                content = self.content_text.get("1.0", tk.END).strip()
                if not content:
                    messagebox.showerror("خطأ", "يرجى إدخال محتوى المنشور")
                    return

                platforms = self.get_selected_platforms()
                if not platforms:
                    messagebox.showerror("خطأ", "يرجى اختيار منصة واحدة على الأقل")
                    return

                hashtags = self.hashtags_var.get().strip()

                # رفع الملفات إذا كانت موجودة
                media_urls = []
                if self.selected_media_files:
                    self.update_status("جاري رفع الملفات...")
                    for file_path in self.selected_media_files:
                        upload_result = ayrshare_client.upload_media(file_path)
                        if upload_result.get('status') == 'success':
                            media_urls.append(upload_result.get('url'))
                        else:
                            messagebox.showerror("خطأ", f"فشل رفع الملف: {upload_result.get('message')}")
                            return

                # النشر
                result = ayrshare_client.post_content(
                    content=content,
                    platforms=platforms,
                    media_urls=media_urls if media_urls else None,
                    hashtags=hashtags if hashtags else None
                )

                if result.get('status') == 'success':
                    # حفظ في قاعدة البيانات
                    post = Post(
                        title=content[:50] + "..." if len(content) > 50 else content,
                        content=content,
                        media_paths=self.selected_media_files,
                        platforms=platforms,
                        hashtags=hashtags,
                        status="published",
                        published_at=datetime.now(),
                        ayrshare_id=result.get('id')
                    )
                    post_id = db.create_post(post)

                    # تسجيل في الإحصائيات
                    for platform in platforms:
                        analytics_manager.record_post_attempt(
                            post_id=str(post_id),
                            platform=platform,
                            post_type='post',
                            content_length=len(content),
                            media_count=len(self.selected_media_files),
                            hashtags_count=len(hashtags.split()) if hashtags else 0,
                            success=True,
                            response_time=result.get('response_time')
                        )

                    self.update_status("تم النشر بنجاح!")

                    # إشعار النجاح
                    notification_manager.show_post_success(platforms, result.get('id'))

                    # مسح النموذج
                    self.clear_form()

                else:
                    error_msg = result.get('message', 'خطأ غير معروف')
                    self.update_status(f"فشل النشر: {error_msg}")

                    # تسجيل الفشل في الإحصائيات
                    for platform in platforms:
                        analytics_manager.record_post_attempt(
                            post_id="failed",
                            platform=platform,
                            post_type='post',
                            content_length=len(content),
                            media_count=len(self.selected_media_files),
                            hashtags_count=len(hashtags.split()) if hashtags else 0,
                            success=False,
                            error_message=error_msg
                        )

                    # إشعار الخطأ
                    notification_manager.show_api_error(error_msg)

            except Exception as e:
                logger.error(f"خطأ في النشر: {e}")
                self.update_status(f"خطأ في النشر: {e}")
                messagebox.showerror("خطأ", f"خطأ في النشر: {e}")

        threading.Thread(target=post_in_thread, daemon=True).start()

    def save_draft(self):
        """حفظ كمسودة"""
        try:
            content = self.content_text.get("1.0", tk.END).strip()
            if not content:
                messagebox.showerror("خطأ", "يرجى إدخال محتوى المنشور")
                return

            platforms = self.get_selected_platforms()
            hashtags = self.hashtags_var.get().strip()

            # إنشاء منشور مسودة
            post = Post(
                title=content[:50] + "..." if len(content) > 50 else content,
                content=content,
                media_paths=self.selected_media_files.copy(),
                platforms=platforms,
                hashtags=hashtags,
                status="draft"
            )

            post_id = db.create_post(post)
            if post_id:
                self.update_status("تم حفظ المسودة")
                messagebox.showinfo("تم الحفظ", "تم حفظ المنشور كمسودة")
                self.clear_form()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ المسودة")

        except Exception as e:
            logger.error(f"خطأ في حفظ المسودة: {e}")
            messagebox.showerror("خطأ", f"خطأ في حفظ المسودة: {e}")

    def preview_post(self):
        """معاينة المنشور"""
        try:
            content = self.content_text.get("1.0", tk.END).strip()
            hashtags = self.hashtags_var.get().strip()
            platforms = self.get_selected_platforms()

            preview_text = f"المحتوى:\n{content}\n\n"
            if hashtags:
                preview_text += f"الهاشتاجات:\n{hashtags}\n\n"
            preview_text += f"المنصات:\n{', '.join(platforms)}\n\n"
            if self.selected_media_files:
                preview_text += f"الملفات:\n" + "\n".join([os.path.basename(f) for f in self.selected_media_files])

            messagebox.showinfo("معاينة المنشور", preview_text)

        except Exception as e:
            logger.error(f"خطأ في المعاينة: {e}")
            messagebox.showerror("خطأ", f"خطأ في المعاينة: {e}")

    def clear_form(self):
        """مسح النموذج"""
        self.content_text.delete("1.0", tk.END)
        self.hashtags_var.set("")
        self.clear_media()
        self.schedule_var.set(False)
        self.toggle_schedule()

    def update_status(self, message: str):
        """تحديث شريط الحالة"""
        self.status_label.configure(text=message)
        self.root.update_idletasks()

    # ===== وظائف النسخ واللصق =====

    def copy_text(self, event=None):
        """نسخ النص المحدد"""
        try:
            # للحصول على النص المحدد في CustomTkinter
            selected_text = self.content_text.get("sel.first", "sel.last")
            if selected_text:
                self.root.clipboard_clear()
                self.root.clipboard_append(selected_text)
                self.update_status("تم نسخ النص")
                return "break"
        except tk.TclError:
            # إذا لم يكن هناك نص محدد، انسخ كل النص
            try:
                all_text = self.content_text.get("1.0", "end-1c")
                if all_text.strip():
                    self.root.clipboard_clear()
                    self.root.clipboard_append(all_text)
                    self.update_status("تم نسخ كل النص")
            except:
                pass
        return "break"

    def paste_text(self, event=None):
        """لصق النص من الحافظة"""
        try:
            clipboard_text = self.root.clipboard_get()
            if clipboard_text:
                # الحصول على موضع المؤشر الحالي
                cursor_pos = self.content_text.index(tk.INSERT)
                # إدراج النص في موضع المؤشر
                self.content_text.insert(cursor_pos, clipboard_text)
                self.update_status("تم لصق النص")
        except tk.TclError:
            pass  # الحافظة فارغة
        return "break"

    def cut_text(self, event=None):
        """قص النص المحدد"""
        try:
            # الحصول على النص المحدد
            selected_text = self.content_text.get("sel.first", "sel.last")
            if selected_text:
                self.root.clipboard_clear()
                self.root.clipboard_append(selected_text)
                # حذف النص المحدد
                self.content_text.delete("sel.first", "sel.last")
                self.update_status("تم قص النص")
        except tk.TclError:
            pass  # لا يوجد نص محدد
        return "break"

    def select_all_text(self, event=None):
        """تحديد جميع النص"""
        self.content_text.tag_add("sel", "1.0", "end-1c")
        self.content_text.mark_set(tk.INSERT, "1.0")
        self.content_text.see(tk.INSERT)
        self.update_status("تم تحديد جميع النص")
        return "break"

    def show_context_menu(self, event):
        """إظهار قائمة السياق عند النقر بالزر الأيمن"""
        try:
            # إنشاء قائمة السياق
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="نسخ (Ctrl+C)", command=self.copy_text)
            context_menu.add_command(label="لصق (Ctrl+V)", command=self.paste_text)
            context_menu.add_command(label="قص (Ctrl+X)", command=self.cut_text)
            context_menu.add_separator()
            context_menu.add_command(label="تحديد الكل (Ctrl+A)", command=self.select_all_text)
            context_menu.add_separator()
            context_menu.add_command(label="مسح النص", command=self.clear_content_text)

            # إظهار القائمة
            context_menu.tk_popup(event.x_root, event.y_root)
        except Exception as e:
            logger.error(f"خطأ في قائمة السياق: {e}")

    def clear_content_text(self):
        """مسح نص المحتوى"""
        self.content_text.delete("1.0", tk.END)
        self.update_status("تم مسح النص")

    # ===== وظائف النسخ واللصق للهاشتاجات =====

    def copy_hashtags(self, event=None):
        """نسخ الهاشتاجات المحددة"""
        try:
            # للحصول على النص المحدد في Entry
            try:
                selected_text = self.hashtags_entry.selection_get()
                if selected_text:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(selected_text)
                    self.update_status("تم نسخ الهاشتاجات")
                    return "break"
            except:
                # إذا لم يكن هناك تحديد، انسخ كل النص
                all_text = self.hashtags_entry.get()
                if all_text:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(all_text)
                    self.update_status("تم نسخ كل الهاشتاجات")
        except:
            pass
        return "break"

    def paste_hashtags(self, event=None):
        """لصق الهاشتاجات من الحافظة"""
        try:
            clipboard_text = self.root.clipboard_get()
            if clipboard_text:
                # الحصول على موضع المؤشر الحالي
                cursor_pos = self.hashtags_entry.index(tk.INSERT)
                # إدراج النص في موضع المؤشر
                self.hashtags_entry.insert(cursor_pos, clipboard_text)
                self.update_status("تم لصق الهاشتاجات")
        except tk.TclError:
            pass
        return "break"

    def cut_hashtags(self, event=None):
        """قص الهاشتاجات المحددة"""
        try:
            # محاولة الحصول على النص المحدد
            try:
                selected_text = self.hashtags_entry.selection_get()
                if selected_text:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(selected_text)
                    # حذف النص المحدد
                    self.hashtags_entry.delete(tk.SEL_FIRST, tk.SEL_LAST)
                    self.update_status("تم قص الهاشتاجات")
            except:
                # إذا لم يكن هناك تحديد، اقطع كل النص
                all_text = self.hashtags_entry.get()
                if all_text:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(all_text)
                    self.hashtags_entry.delete(0, tk.END)
                    self.update_status("تم قص كل الهاشتاجات")
        except:
            pass
        return "break"

    def select_all_hashtags(self, event=None):
        """تحديد جميع الهاشتاجات"""
        try:
            self.hashtags_entry.select_range(0, tk.END)
            self.hashtags_entry.icursor(tk.END)
            self.update_status("تم تحديد جميع الهاشتاجات")
        except:
            pass
        return "break"

    # ===== وظائف النسخ واللصق لحقل API Key =====

    def copy_api_key(self, event=None):
        """نسخ API Key"""
        try:
            try:
                selected_text = self.api_key_entry.selection_get()
                if selected_text:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(selected_text)
                    self.update_status("تم نسخ API Key المحدد")
                    return "break"
            except:
                all_text = self.api_key_entry.get()
                if all_text:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(all_text)
                    self.update_status("تم نسخ API Key")
        except:
            pass
        return "break"

    def paste_api_key(self, event=None):
        """لصق API Key"""
        try:
            clipboard_text = self.root.clipboard_get()
            if clipboard_text:
                cursor_pos = self.api_key_entry.index(tk.INSERT)
                self.api_key_entry.insert(cursor_pos, clipboard_text)
                self.update_status("تم لصق API Key")
        except tk.TclError:
            pass
        return "break"

    def cut_api_key(self, event=None):
        """قص API Key"""
        try:
            try:
                selected_text = self.api_key_entry.selection_get()
                if selected_text:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(selected_text)
                    self.api_key_entry.delete(tk.SEL_FIRST, tk.SEL_LAST)
                    self.update_status("تم قص API Key المحدد")
            except:
                all_text = self.api_key_entry.get()
                if all_text:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(all_text)
                    self.api_key_entry.delete(0, tk.END)
                    self.update_status("تم قص API Key")
        except:
            pass
        return "break"

    def select_all_api_key(self, event=None):
        """تحديد كل API Key"""
        try:
            self.api_key_entry.select_range(0, tk.END)
            self.api_key_entry.icursor(tk.END)
            self.update_status("تم تحديد كل API Key")
        except:
            pass
        return "break"

    def show_api_key_context_menu(self, event):
        """إظهار قائمة السياق لحقل API Key"""
        try:
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="نسخ (Ctrl+C)", command=self.copy_api_key)
            context_menu.add_command(label="لصق (Ctrl+V)", command=self.paste_api_key)
            context_menu.add_command(label="قص (Ctrl+X)", command=self.cut_api_key)
            context_menu.add_separator()
            context_menu.add_command(label="تحديد الكل (Ctrl+A)", command=self.select_all_api_key)
            context_menu.add_separator()
            context_menu.add_command(label="مسح API Key", command=lambda: self.api_key_entry.delete(0, tk.END))
            context_menu.tk_popup(event.x_root, event.y_root)
        except Exception as e:
            logger.error(f"خطأ في قائمة السياق API Key: {e}")

    # ===== وظائف النسخ واللصق لحقل Profile Key =====

    def copy_profile_key(self, event=None):
        """نسخ Profile Key"""
        try:
            try:
                selected_text = self.profile_key_entry.selection_get()
                if selected_text:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(selected_text)
                    self.update_status("تم نسخ Profile Key المحدد")
                    return "break"
            except:
                all_text = self.profile_key_entry.get()
                if all_text:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(all_text)
                    self.update_status("تم نسخ Profile Key")
        except:
            pass
        return "break"

    def paste_profile_key(self, event=None):
        """لصق Profile Key"""
        try:
            clipboard_text = self.root.clipboard_get()
            if clipboard_text:
                cursor_pos = self.profile_key_entry.index(tk.INSERT)
                self.profile_key_entry.insert(cursor_pos, clipboard_text)
                self.update_status("تم لصق Profile Key")
        except tk.TclError:
            pass
        return "break"

    def cut_profile_key(self, event=None):
        """قص Profile Key"""
        try:
            try:
                selected_text = self.profile_key_entry.selection_get()
                if selected_text:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(selected_text)
                    self.profile_key_entry.delete(tk.SEL_FIRST, tk.SEL_LAST)
                    self.update_status("تم قص Profile Key المحدد")
            except:
                all_text = self.profile_key_entry.get()
                if all_text:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(all_text)
                    self.profile_key_entry.delete(0, tk.END)
                    self.update_status("تم قص Profile Key")
        except:
            pass
        return "break"

    def select_all_profile_key(self, event=None):
        """تحديد كل Profile Key"""
        try:
            self.profile_key_entry.select_range(0, tk.END)
            self.profile_key_entry.icursor(tk.END)
            self.update_status("تم تحديد كل Profile Key")
        except:
            pass
        return "break"

    # ===== وظائف القوائم الجديدة =====

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        def backup_thread():
            try:
                self.update_status("جاري إنشاء نسخة احتياطية...")
                backup_path = backup_manager.create_backup()

                if backup_path:
                    self.update_status("تم إنشاء النسخة الاحتياطية")
                    notification_manager.show_success("نسخ احتياطي", "تم إنشاء النسخة الاحتياطية بنجاح")
                else:
                    self.update_status("فشل في إنشاء النسخة الاحتياطية")
                    notification_manager.show_error("نسخ احتياطي", "فشل في إنشاء النسخة الاحتياطية")

            except Exception as e:
                logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
                self.update_status("خطأ في النسخ الاحتياطي")

        threading.Thread(target=backup_thread, daemon=True).start()

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        try:
            from tkinter import filedialog

            file_path = filedialog.askopenfilename(
                title="اختر النسخة الاحتياطية",
                filetypes=[("ملفات النسخ الاحتياطي", "*.zip"), ("جميع الملفات", "*.*")]
            )

            if file_path:
                if messagebox.askyesno("تأكيد", "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟"):
                    def restore_thread():
                        try:
                            self.update_status("جاري استعادة النسخة الاحتياطية...")
                            success = backup_manager.restore_backup(file_path)

                            if success:
                                self.update_status("تم استعادة النسخة الاحتياطية")
                                notification_manager.show_success("استعادة", "تم استعادة النسخة الاحتياطية بنجاح")
                            else:
                                self.update_status("فشل في الاستعادة")
                                notification_manager.show_error("استعادة", "فشل في استعادة النسخة الاحتياطية")

                        except Exception as e:
                            logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
                            self.update_status("خطأ في الاستعادة")

                    threading.Thread(target=restore_thread, daemon=True).start()

        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            messagebox.showerror("خطأ", f"خطأ في الاستعادة: {e}")

    def export_settings(self):
        """تصدير الإعدادات"""
        try:
            from tkinter import filedialog

            file_path = filedialog.asksaveasfilename(
                title="تصدير الإعدادات",
                defaultextension=".json",
                filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
            )

            if file_path:
                if config.export_config(file_path):
                    notification_manager.show_success("تصدير", "تم تصدير الإعدادات بنجاح")
                else:
                    notification_manager.show_error("تصدير", "فشل في تصدير الإعدادات")

        except Exception as e:
            logger.error(f"خطأ في تصدير الإعدادات: {e}")
            messagebox.showerror("خطأ", f"خطأ في التصدير: {e}")

    def import_settings(self):
        """استيراد الإعدادات"""
        try:
            from tkinter import filedialog

            file_path = filedialog.askopenfilename(
                title="استيراد الإعدادات",
                filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
            )

            if file_path:
                if messagebox.askyesno("تأكيد", "هل أنت متأكد من استيراد هذه الإعدادات؟"):
                    if config.import_config(file_path):
                        notification_manager.show_success("استيراد", "تم استيراد الإعدادات بنجاح")
                        messagebox.showinfo("نجح", "تم استيراد الإعدادات\nسيتم إعادة تشغيل البرنامج")
                        self.restart_app()
                    else:
                        notification_manager.show_error("استيراد", "فشل في استيراد الإعدادات")

        except Exception as e:
            logger.error(f"خطأ في استيراد الإعدادات: {e}")
            messagebox.showerror("خطأ", f"خطأ في الاستيراد: {e}")

    def open_advanced_settings(self):
        """فتح نافذة الإعدادات المتقدمة"""
        try:
            advanced_settings = AdvancedSettingsWindow(self.root)

        except Exception as e:
            logger.error(f"خطأ في فتح الإعدادات المتقدمة: {e}")
            messagebox.showerror("خطأ", f"خطأ في فتح الإعدادات المتقدمة: {e}")

    def show_analytics_report(self):
        """عرض تقرير الإحصائيات"""
        try:
            report = analytics_manager.generate_summary_report()

            if not report:
                messagebox.showinfo("الإحصائيات", "لا توجد إحصائيات متاحة")
                return

            # نافذة التقرير
            report_window = ctk.CTkToplevel(self.root)
            report_window.title("تقرير الإحصائيات")
            report_window.geometry("600x400")

            # محتوى التقرير
            report_text = ctk.CTkTextbox(report_window)
            report_text.pack(fill="both", expand=True, padx=10, pady=10)

            # إضافة محتوى التقرير
            summary = report.get('summary', {})
            report_text.insert("end", "📊 ملخص الإحصائيات\n")
            report_text.insert("end", "="*50 + "\n\n")

            report_text.insert("end", f"إجمالي المنشورات: {summary.get('total_posts', 0)}\n")
            report_text.insert("end", f"المنشورات الناجحة: {summary.get('successful_posts', 0)}\n")
            report_text.insert("end", f"معدل النجاح: {summary.get('success_rate', 0):.1f}%\n\n")

            report_text.configure(state="disabled")

        except Exception as e:
            logger.error(f"خطأ في عرض تقرير الإحصائيات: {e}")
            messagebox.showerror("خطأ", f"خطأ في عرض التقرير: {e}")

    def check_updates(self):
        """فحص التحديثات"""
        def check_thread():
            try:
                self.update_status("جاري فحص التحديثات...")
                has_update, update_info = update_manager.check_for_updates(show_no_updates=True)

                if has_update and update_info:
                    self.update_status("تحديث متاح")
                    notification_manager.show_info("تحديث", f"تحديث متاح: الإصدار {update_info['version']}")
                else:
                    self.update_status("لا توجد تحديثات")
                    notification_manager.show_info("تحديث", "لا توجد تحديثات متاحة")

            except Exception as e:
                logger.error(f"خطأ في فحص التحديثات: {e}")
                self.update_status("خطأ في فحص التحديثات")

        threading.Thread(target=check_thread, daemon=True).start()

    def auto_check_updates(self):
        """فحص التحديثات التلقائي"""
        try:
            has_update, update_info = update_manager.check_for_updates()

            if has_update and update_info:
                notification_manager.show_info("تحديث متاح",
                                              f"تحديث جديد متاح: الإصدار {update_info['version']}")

        except Exception as e:
            logger.error(f"خطأ في فحص التحديثات التلقائي: {e}")

    def clear_cache(self):
        """مسح ذاكرة التخزين المؤقت"""
        try:
            if messagebox.askyesno("تأكيد", "هل أنت متأكد من مسح ذاكرة التخزين المؤقت؟"):
                import shutil
                import os

                cache_dirs = ['__pycache__', 'temp']
                cleared_count = 0

                for cache_dir in cache_dirs:
                    if os.path.exists(cache_dir):
                        shutil.rmtree(cache_dir)
                        cleared_count += 1

                notification_manager.show_success("تنظيف", f"تم مسح {cleared_count} مجلد مؤقت")

        except Exception as e:
            logger.error(f"خطأ في مسح ذاكرة التخزين المؤقت: {e}")
            messagebox.showerror("خطأ", f"خطأ في المسح: {e}")

    def restart_app(self):
        """إعادة تشغيل التطبيق"""
        try:
            if messagebox.askyesno("إعادة تشغيل", "هل أنت متأكد من إعادة تشغيل البرنامج؟"):
                import sys
                import subprocess

                # إعادة تشغيل البرنامج
                subprocess.Popen([sys.executable] + sys.argv)

                # إنهاء العملية الحالية
                self.root.quit()
                sys.exit(0)

        except Exception as e:
            logger.error(f"خطأ في إعادة تشغيل التطبيق: {e}")
            messagebox.showerror("خطأ", f"خطأ في إعادة التشغيل: {e}")

    def test_notifications(self):
        """اختبار الإشعارات"""
        try:
            notification_manager.show_success("اختبار", "هذا اختبار لإشعار النجاح")
            notification_manager.show_info("اختبار", "هذا اختبار لإشعار المعلومات")
            notification_manager.show_warning("اختبار", "هذا اختبار لإشعار التحذير")
            notification_manager.show_error("اختبار", "هذا اختبار لإشعار الخطأ")

        except Exception as e:
            logger.error(f"خطأ في اختبار الإشعارات: {e}")
            messagebox.showerror("خطأ", f"خطأ في اختبار الإشعارات: {e}")

    def show_help(self):
        """عرض دليل الاستخدام"""
        try:
            import webbrowser
            import os

            if os.path.exists("README.md"):
                os.startfile("README.md")  # Windows
            else:
                messagebox.showinfo("مساعدة", "ملف دليل الاستخدام غير موجود")

        except Exception as e:
            logger.error(f"خطأ في عرض المساعدة: {e}")
            messagebox.showerror("خطأ", f"خطأ في عرض المساعدة: {e}")

    def show_faq(self):
        """عرض الأسئلة الشائعة"""
        try:
            import os

            if os.path.exists("FAQ.md"):
                os.startfile("FAQ.md")  # Windows
            else:
                messagebox.showinfo("أسئلة شائعة", "ملف الأسئلة الشائعة غير موجود")

        except Exception as e:
            logger.error(f"خطأ في عرض الأسئلة الشائعة: {e}")
            messagebox.showerror("خطأ", f"خطأ في عرض الأسئلة الشائعة: {e}")

    def show_about(self):
        """عرض معلومات البرنامج"""
        try:
            about_text = f"""
🚀 Ayrshare Social Media Manager

الإصدار: {config.get('app.version', '1.0.0')}

برنامج إدارة وسائل التواصل الاجتماعي باستخدام Ayrshare API
مع واجهة رسومية متقدمة باللغة العربية

الميزات:
• نشر على Facebook, Instagram, TikTok
• جدولة المنشورات
• تخطي حدود المنصات
• نسخ احتياطي تلقائي
• إحصائيات مفصلة
• إشعارات ذكية

تطوير: فريق التطوير
الترخيص: MIT License
            """

            messagebox.showinfo("حول البرنامج", about_text)

        except Exception as e:
            logger.error(f"خطأ في عرض معلومات البرنامج: {e}")
            messagebox.showerror("خطأ", f"خطأ في عرض المعلومات: {e}")

    # ===== وظائف الجدولة =====

    def refresh_schedule_list(self):
        """تحديث قائمة المنشورات المجدولة"""
        try:
            # مسح القائمة الحالية
            for item in self.schedule_tree.get_children():
                self.schedule_tree.delete(item)

            # الحصول على المنشورات المجدولة
            scheduled_posts = db.get_scheduled_posts()

            for scheduled_post in scheduled_posts:
                # الحصول على بيانات المنشور
                post = db.get_post(scheduled_post.post_id)
                if post:
                    platforms_str = ", ".join(post.platforms)
                    scheduled_time_str = scheduled_post.scheduled_time.strftime("%Y-%m-%d %H:%M")
                    status = "نشط" if scheduled_post.is_active else "معطل"

                    self.schedule_tree.insert("", "end", values=(
                        post.title,
                        platforms_str,
                        scheduled_time_str,
                        status
                    ))

            self.update_status(f"تم تحديث قائمة الجدولة - {len(scheduled_posts)} منشور")

        except Exception as e:
            logger.error(f"خطأ في تحديث قائمة الجدولة: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحديث القائمة: {e}")

    def delete_scheduled_post(self):
        """حذف منشور مجدول"""
        try:
            selected_item = self.schedule_tree.selection()
            if not selected_item:
                messagebox.showwarning("تحذير", "يرجى اختيار منشور لحذفه")
                return

            if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المنشور المجدول؟"):
                # هنا يمكن إضافة منطق الحذف
                self.refresh_schedule_list()
                self.update_status("تم حذف المنشور المجدول")

        except Exception as e:
            logger.error(f"خطأ في حذف المنشور المجدول: {e}")
            messagebox.showerror("خطأ", f"خطأ في الحذف: {e}")

    def edit_schedule_time(self):
        """تعديل وقت الجدولة"""
        try:
            selected_item = self.schedule_tree.selection()
            if not selected_item:
                messagebox.showwarning("تحذير", "يرجى اختيار منشور لتعديل وقته")
                return

            # هنا يمكن إضافة نافذة تعديل الوقت
            messagebox.showinfo("قريباً", "ميزة تعديل الوقت ستكون متاحة قريباً")

        except Exception as e:
            logger.error(f"خطأ في تعديل وقت الجدولة: {e}")
            messagebox.showerror("خطأ", f"خطأ في التعديل: {e}")

    # ===== وظائف الإعدادات =====

    def toggle_bypass(self):
        """تبديل تفعيل تخطي الحدود"""
        try:
            enabled = self.bypass_var.get()
            rate_limiter.enable_bypass(enabled)
            status = "مفعل" if enabled else "معطل"
            self.update_status(f"تخطي الحدود: {status}")

        except Exception as e:
            logger.error(f"خطأ في تبديل تخطي الحدود: {e}")
            messagebox.showerror("خطأ", f"خطأ في تبديل الإعداد: {e}")

    def apply_delay_setting(self):
        """تطبيق إعداد التأخير"""
        try:
            delay = int(self.delay_var.get())
            if delay < 0:
                messagebox.showerror("خطأ", "التأخير يجب أن يكون رقماً موجباً")
                return

            rate_limiter.set_bypass_delay(delay)
            self.update_status(f"تم تعيين التأخير: {delay} ثانية")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح للتأخير")
        except Exception as e:
            logger.error(f"خطأ في تطبيق إعداد التأخير: {e}")
            messagebox.showerror("خطأ", f"خطأ في تطبيق الإعداد: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ بيانات API
            config.set('api_key', self.api_key_var.get())
            config.set('profile_key', self.profile_key_var.get())

            # حفظ الهاشتاجات الافتراضية
            config.set('default_hashtags.facebook', self.fb_hashtags_var.get())
            config.set('default_hashtags.instagram', self.ig_hashtags_var.get())
            config.set('default_hashtags.tiktok', self.tt_hashtags_var.get())

            self.update_status("تم حفظ الإعدادات")
            # إعادة ربط اختصارات النسخ واللصق بعد حفظ الإعدادات
            self.rebind_copy_paste_shortcuts()
            messagebox.showinfo("تم الحفظ", "تم حفظ جميع الإعدادات بنجاح")

        except Exception as e:
            logger.error(f"خطأ في حفظ الإعدادات: {e}")
            messagebox.showerror("خطأ", f"خطأ في حفظ الإعدادات: {e}")

    def reset_settings(self):
        """استعادة الإعدادات الافتراضية"""
        try:
            if messagebox.askyesno("تأكيد الاستعادة", "هل أنت متأكد من استعادة الإعدادات الافتراضية؟"):
                config.reset_to_default()

                # تحديث الواجهة
                self.api_key_var.set("")
                self.profile_key_var.set("")
                self.fb_hashtags_var.set(config.get('default_hashtags.facebook', ''))
                self.ig_hashtags_var.set(config.get('default_hashtags.instagram', ''))
                self.tt_hashtags_var.set(config.get('default_hashtags.tiktok', ''))
                self.bypass_var.set(False)
                self.delay_var.set("60")

                self.update_status("تم استعادة الإعدادات الافتراضية")
                messagebox.showinfo("تم الاستعادة", "تم استعادة الإعدادات الافتراضية")

        except Exception as e:
            logger.error(f"خطأ في استعادة الإعدادات: {e}")
            messagebox.showerror("خطأ", f"خطأ في الاستعادة: {e}")

    # ===== وظائف الإحصائيات =====

    def update_stats(self):
        """تحديث الإحصائيات"""
        try:
            # مسح الإحصائيات الحالية
            for widget in self.stats_frame.winfo_children():
                widget.destroy()

            platforms = ['facebook', 'instagram', 'tiktok']

            for platform in platforms:
                stats = rate_limiter.get_usage_stats(platform)
                if stats:
                    # إطار إحصائيات المنصة
                    platform_frame = ctk.CTkFrame(self.stats_frame)
                    platform_frame.pack(fill="x", padx=5, pady=5)

                    # عنوان المنصة
                    platform_name = {
                        'facebook': 'Facebook',
                        'instagram': 'Instagram',
                        'tiktok': 'TikTok'
                    }.get(platform, platform)

                    ctk.CTkLabel(platform_frame, text=platform_name,
                               font=("Arial", 14, "bold")).pack(anchor="w", padx=5, pady=2)

                    # الإحصائيات الساعية
                    hourly_text = f"الاستخدام الساعي: {stats.get('hourly_used', 0)}/{stats.get('hourly_limit', 0)}"
                    ctk.CTkLabel(platform_frame, text=hourly_text).pack(anchor="w", padx=10)

                    # الإحصائيات اليومية
                    daily_text = f"الاستخدام اليومي: {stats.get('daily_used', 0)}/{stats.get('daily_limit', 0)}"
                    ctk.CTkLabel(platform_frame, text=daily_text).pack(anchor="w", padx=10)

                    # إحصائيات خاصة
                    if platform == 'facebook' and 'reels_daily_used' in stats:
                        reels_text = f"الريلز اليومي: {stats.get('reels_daily_used', 0)}/{stats.get('reels_daily_limit', 0)}"
                        ctk.CTkLabel(platform_frame, text=reels_text).pack(anchor="w", padx=10)

                    elif platform == 'instagram' and 'stories_daily_used' in stats:
                        stories_text = f"الستوريز اليومي: {stats.get('stories_daily_used', 0)}/{stats.get('stories_daily_limit', 0)}"
                        ctk.CTkLabel(platform_frame, text=stories_text).pack(anchor="w", padx=10)

            self.update_status("تم تحديث الإحصائيات")

        except Exception as e:
            logger.error(f"خطأ في تحديث الإحصائيات: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحديث الإحصائيات: {e}")

    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.mainloop()
        except Exception as e:
            logger.error(f"خطأ في تشغيل التطبيق: {e}")
            messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    app = MainWindow()
    app.run()
