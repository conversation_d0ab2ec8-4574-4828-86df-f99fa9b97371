# -*- coding: utf-8 -*-
"""
اختبار الاسكرول وإصلاح API Key
"""
import tkinter as tk
import customtkinter as ctk

def test_scroll_and_api_fix():
    """اختبار الاسكرول وإصلاح API Key"""
    print("🧪 اختبار الاسكرول وإصلاح API Key")
    print("=" * 50)
    
    # إنشاء نافذة اختبار
    root = ctk.CTk()
    root.title("اختبار الاسكرول وإصلاح API Key")
    root.geometry("900x700")
    
    # إنشاء تبويبات
    notebook = ctk.CTkTabview(root)
    notebook.pack(fill="both", expand=True, padx=10, pady=10)
    
    # تبويب اختبار الاسكرول
    scroll_tab = notebook.add("اختبار الاسكرول")
    
    # إنشاء إطار قابل للتمرير
    scroll_frame = ctk.CTkScrollableFrame(scroll_tab)
    scroll_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    ctk.CTkLabel(scroll_frame, text="اختبار الاسكرول", 
                font=("Arial", 18, "bold")).pack(pady=10)
    
    # إضافة محتوى كثير لاختبار الاسكرول
    for i in range(50):
        frame = ctk.CTkFrame(scroll_frame)
        frame.pack(fill="x", padx=10, pady=2)
        
        ctk.CTkLabel(frame, text=f"عنصر رقم {i+1}").pack(side="left", padx=10)
        ctk.CTkEntry(frame, placeholder_text=f"حقل إدخال {i+1}").pack(side="left", padx=5, fill="x", expand=True)
        ctk.CTkButton(frame, text=f"زر {i+1}", width=80).pack(side="left", padx=5)
    
    # تبويب اختبار API Key
    api_tab = notebook.add("اختبار API Key")
    
    # إنشاء إطار قابل للتمرير للـ API
    api_scroll_frame = ctk.CTkScrollableFrame(api_tab)
    api_scroll_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    ctk.CTkLabel(api_scroll_frame, text="اختبار API Key مع رسائل خطأ محسنة", 
                font=("Arial", 18, "bold")).pack(pady=10)
    
    # حقل API Key
    api_frame = ctk.CTkFrame(api_scroll_frame)
    api_frame.pack(fill="x", padx=10, pady=10)
    
    ctk.CTkLabel(api_frame, text="API Key:", width=100).pack(side="left", padx=5)
    api_key_var = tk.StringVar()
    api_key_entry = ctk.CTkEntry(api_frame, textvariable=api_key_var, width=400, show="*")
    api_key_entry.pack(side="left", padx=5, fill="x", expand=True)
    
    # شريط الحالة
    status_frame = ctk.CTkFrame(api_scroll_frame)
    status_frame.pack(fill="x", padx=10, pady=5)
    
    status_label = ctk.CTkLabel(status_frame, text="جاهز للاختبار")
    status_label.pack(pady=5)
    
    # محاكاة اختبار API Key
    def test_api_key():
        api_key = api_key_var.get().strip()
        
        if not api_key:
            status_label.configure(text="❌ يرجى إدخال API Key")
            from tkinter import messagebox
            messagebox.showwarning("تحذير", "يرجى إدخال API Key صالح أولاً")
            return
        
        if api_key == "test_valid_key":
            status_label.configure(text="✅ API Key صالح - تم الاتصال بنجاح")
            from tkinter import messagebox
            messagebox.showinfo("نجح الاتصال", "تم الاتصال بـ Ayrshare API بنجاح!")
        else:
            status_label.configure(text="❌ API Key غير صالح")
            from tkinter import messagebox
            error_msg = """API Key غير صالح!

الرجاء التأكد من:
1. API Key صحيح ومنسوخ بالكامل
2. لم تنته صلاحية API Key
3. الحساب نشط في Ayrshare

للحصول على API Key صالح:
• اذهب إلى: https://app.ayrshare.com
• سجل دخول لحسابك
• اذهب لقسم "API Key"
• انسخ API Key الجديد

للاختبار: استخدم "test_valid_key" """
            messagebox.showerror("API Key غير صالح", error_msg)
    
    # أزرار الاختبار
    buttons_frame = ctk.CTkFrame(api_scroll_frame)
    buttons_frame.pack(fill="x", padx=10, pady=10)
    
    ctk.CTkButton(buttons_frame, text="اختبار API Key", 
                 command=test_api_key).pack(side="left", padx=5)
    
    def set_valid_key():
        api_key_var.set("test_valid_key")
        status_label.configure(text="تم إدخال API Key صالح للاختبار")
    
    ctk.CTkButton(buttons_frame, text="إدخال API Key صالح", 
                 command=set_valid_key).pack(side="left", padx=5)
    
    def set_invalid_key():
        api_key_var.set("invalid_key_123")
        status_label.configure(text="تم إدخال API Key غير صالح للاختبار")
    
    ctk.CTkButton(buttons_frame, text="إدخال API Key غير صالح", 
                 command=set_invalid_key).pack(side="left", padx=5)
    
    def clear_key():
        api_key_var.set("")
        status_label.configure(text="تم مسح API Key")
    
    ctk.CTkButton(buttons_frame, text="مسح API Key", 
                 command=clear_key).pack(side="left", padx=5)
    
    # إضافة محتوى إضافي لاختبار الاسكرول
    info_frame = ctk.CTkFrame(api_scroll_frame)
    info_frame.pack(fill="x", padx=10, pady=10)
    
    info_text = """🎯 تعليمات الاختبار:

1. اختبار الاسكرول:
   • اذهب لتبويب "اختبار الاسكرول"
   • جرب التمرير لأعلى وأسفل
   • تأكد أن جميع العناصر مرئية

2. اختبار API Key:
   • جرب إدخال API Key فارغ واضغط "اختبار"
   • جرب إدخال API Key غير صالح
   • جرب إدخال API Key صالح (test_valid_key)
   • لاحظ رسائل الخطأ المحسنة

3. اختبار النسخ واللصق:
   • جرب نسخ ولصق في حقل API Key
   • استخدم Ctrl+C و Ctrl+V
   • جرب النقر الأيمن

✅ إذا عمل كل شيء، فالإصلاحات نجحت!"""
    
    ctk.CTkLabel(info_frame, text=info_text, justify="left").pack(pady=10)
    
    # تبويب اختبار النسخ واللصق
    copy_paste_tab = notebook.add("النسخ واللصق")
    
    copy_paste_scroll = ctk.CTkScrollableFrame(copy_paste_tab)
    copy_paste_scroll.pack(fill="both", expand=True, padx=10, pady=10)
    
    ctk.CTkLabel(copy_paste_scroll, text="اختبار النسخ واللصق مع الاسكرول", 
                font=("Arial", 18, "bold")).pack(pady=10)
    
    # حقول متعددة لاختبار النسخ واللصق
    for i in range(10):
        test_frame = ctk.CTkFrame(copy_paste_scroll)
        test_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(test_frame, text=f"حقل {i+1}:", width=80).pack(side="left", padx=5)
        test_var = tk.StringVar(value=f"نص تجريبي {i+1}")
        test_entry = ctk.CTkEntry(test_frame, textvariable=test_var)
        test_entry.pack(side="left", padx=5, fill="x", expand=True)
        
        # ربط النسخ واللصق
        def make_copy_paste(entry, label_text):
            def copy_text(event=None):
                try:
                    selected = entry.selection_get()
                    if selected:
                        root.clipboard_clear()
                        root.clipboard_append(selected)
                        print(f"تم نسخ من {label_text}: {selected}")
                except:
                    text = entry.get()
                    if text:
                        root.clipboard_clear()
                        root.clipboard_append(text)
                        print(f"تم نسخ كل النص من {label_text}: {text}")
                return "break"
            
            def paste_text(event=None):
                try:
                    clipboard_text = root.clipboard_get()
                    if clipboard_text:
                        cursor_pos = entry.index(tk.INSERT)
                        entry.insert(cursor_pos, clipboard_text)
                        print(f"تم لصق في {label_text}: {clipboard_text}")
                except:
                    pass
                return "break"
            
            entry.bind("<Control-c>", copy_text)
            entry.bind("<Control-v>", paste_text)
        
        make_copy_paste(test_entry, f"حقل {i+1}")
    
    def close_test():
        print("✅ اختبار الاسكرول وإصلاح API Key مكتمل")
        root.destroy()
    
    # زر الإغلاق
    close_frame = ctk.CTkFrame(root)
    close_frame.pack(fill="x", padx=10, pady=5)
    
    ctk.CTkButton(close_frame, text="إغلاق الاختبار", 
                 command=close_test).pack(pady=5)
    
    print("🎯 تعليمات الاختبار:")
    print("1. جرب التمرير في جميع التبويبات")
    print("2. اختبر API Key بقيم مختلفة")
    print("3. جرب النسخ واللصق في الحقول")
    print("4. تأكد من رسائل الخطأ المحسنة")
    print("\n✅ إذا عمل كل شيء، فالإصلاحات نجحت!")
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    try:
        test_scroll_and_api_fix()
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
