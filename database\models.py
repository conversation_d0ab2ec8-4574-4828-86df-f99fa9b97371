# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات
"""
import sqlite3
from datetime import datetime
from typing import Optional, List, Dict, Any
from dataclasses import dataclass, asdict
import json

@dataclass
class Post:
    """نموذج المنشور"""
    id: Optional[int] = None
    title: str = ""
    content: str = ""
    media_paths: List[str] = None
    platforms: List[str] = None
    hashtags: str = ""
    scheduled_time: Optional[datetime] = None
    status: str = "draft"  # draft, scheduled, published, failed
    created_at: Optional[datetime] = None
    published_at: Optional[datetime] = None
    ayrshare_id: Optional[str] = None
    error_message: Optional[str] = None

    def __post_init__(self):
        if self.media_paths is None:
            self.media_paths = []
        if self.platforms is None:
            self.platforms = []
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class ScheduledPost:
    """نموذج المنشور المجدول"""
    id: Optional[int] = None
    post_id: int = 0
    scheduled_time: datetime = None
    repeat_type: str = "none"  # none, daily, weekly, monthly
    repeat_interval: int = 1
    repeat_end_date: Optional[datetime] = None
    is_active: bool = True
    created_at: Optional[datetime] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class RateLimit:
    """نموذج حدود المعدل"""
    id: Optional[int] = None
    platform: str = ""
    limit_type: str = ""  # hourly, daily
    current_count: int = 0
    max_count: int = 0
    reset_time: datetime = None
    created_at: Optional[datetime] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class ApiUsage:
    """نموذج استخدام API"""
    id: Optional[int] = None
    endpoint: str = ""
    method: str = ""
    status_code: int = 0
    response_time: float = 0.0
    request_data: str = ""
    response_data: str = ""
    created_at: Optional[datetime] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class MediaFile:
    """نموذج ملف الوسائط"""
    id: Optional[int] = None
    filename: str = ""
    file_path: str = ""
    file_type: str = ""  # image, video
    file_size: int = 0
    duration: Optional[float] = None  # للفيديوهات
    dimensions: Optional[str] = None  # العرض x الارتفاع
    thumbnail_path: Optional[str] = None
    uploaded_at: Optional[datetime] = None

    def __post_init__(self):
        if self.uploaded_at is None:
            self.uploaded_at = datetime.now()

class DatabaseHelper:
    """مساعد قاعدة البيانات"""

    @staticmethod
    def dict_factory(cursor, row):
        """تحويل صفوف قاعدة البيانات إلى قواميس"""
        d = {}
        for idx, col in enumerate(cursor.description):
            d[col[0]] = row[idx]
        return d

    @staticmethod
    def serialize_list(data: List) -> str:
        """تحويل قائمة إلى JSON"""
        return json.dumps(data, ensure_ascii=False)

    @staticmethod
    def deserialize_list(data: str) -> List:
        """تحويل JSON إلى قائمة"""
        try:
            return json.loads(data) if data else []
        except:
            return []

    @staticmethod
    def serialize_datetime(dt: Optional[datetime]) -> Optional[str]:
        """تحويل التاريخ إلى نص"""
        return dt.isoformat() if dt else None

    @staticmethod
    def deserialize_datetime(dt_str: Optional[str]) -> Optional[datetime]:
        """تحويل النص إلى تاريخ"""
        try:
            return datetime.fromisoformat(dt_str) if dt_str else None
        except:
            return None

    @staticmethod
    def post_to_dict(post: Post) -> Dict[str, Any]:
        """تحويل المنشور إلى قاموس لحفظه في قاعدة البيانات"""
        data = asdict(post)
        data['media_paths'] = DatabaseHelper.serialize_list(post.media_paths)
        data['platforms'] = DatabaseHelper.serialize_list(post.platforms)
        data['created_at'] = DatabaseHelper.serialize_datetime(post.created_at)
        data['published_at'] = DatabaseHelper.serialize_datetime(post.published_at)
        data['scheduled_time'] = DatabaseHelper.serialize_datetime(post.scheduled_time)
        return data

    @staticmethod
    def dict_to_post(data: Dict[str, Any]) -> Post:
        """تحويل القاموس إلى منشور"""
        data['media_paths'] = DatabaseHelper.deserialize_list(data.get('media_paths', ''))
        data['platforms'] = DatabaseHelper.deserialize_list(data.get('platforms', ''))
        data['created_at'] = DatabaseHelper.deserialize_datetime(data.get('created_at'))
        data['published_at'] = DatabaseHelper.deserialize_datetime(data.get('published_at'))
        data['scheduled_time'] = DatabaseHelper.deserialize_datetime(data.get('scheduled_time'))
        return Post(**data)

    @staticmethod
    def scheduled_post_to_dict(scheduled_post: ScheduledPost) -> Dict[str, Any]:
        """تحويل المنشور المجدول إلى قاموس"""
        data = asdict(scheduled_post)
        data['scheduled_time'] = DatabaseHelper.serialize_datetime(scheduled_post.scheduled_time)
        data['repeat_end_date'] = DatabaseHelper.serialize_datetime(scheduled_post.repeat_end_date)
        data['created_at'] = DatabaseHelper.serialize_datetime(scheduled_post.created_at)
        return data

    @staticmethod
    def dict_to_scheduled_post(data: Dict[str, Any]) -> ScheduledPost:
        """تحويل القاموس إلى منشور مجدول"""
        data['scheduled_time'] = DatabaseHelper.deserialize_datetime(data.get('scheduled_time'))
        data['repeat_end_date'] = DatabaseHelper.deserialize_datetime(data.get('repeat_end_date'))
        data['created_at'] = DatabaseHelper.deserialize_datetime(data.get('created_at'))
        return ScheduledPost(**data)

    @staticmethod
    def rate_limit_to_dict(rate_limit: RateLimit) -> Dict[str, Any]:
        """تحويل حد المعدل إلى قاموس"""
        data = asdict(rate_limit)
        data['reset_time'] = DatabaseHelper.serialize_datetime(rate_limit.reset_time)
        data['created_at'] = DatabaseHelper.serialize_datetime(rate_limit.created_at)
        return data

    @staticmethod
    def dict_to_rate_limit(data: Dict[str, Any]) -> RateLimit:
        """تحويل القاموس إلى حد معدل"""
        data['reset_time'] = DatabaseHelper.deserialize_datetime(data.get('reset_time'))
        data['created_at'] = DatabaseHelper.deserialize_datetime(data.get('created_at'))
        return RateLimit(**data)
