# -*- coding: utf-8 -*-
"""
فحص نهائي للتأكد من أن كل شيء يعمل
"""
import sys
import os

def check_all():
    """فحص شامل"""
    print("🔍 فحص نهائي شامل")
    print("=" * 50)
    
    # 1. فحص Python
    print(f"✅ Python {sys.version.split()[0]}")
    
    # 2. فحص المكتبات الأساسية
    try:
        import requests
        print("✅ requests متوفر")
    except ImportError:
        print("❌ requests مفقود")
        return False
    
    try:
        import customtkinter
        print("✅ customtkinter متوفر")
    except ImportError:
        print("❌ customtkinter مفقود")
        return False
    
    try:
        from PIL import Image
        print("✅ Pillow متوفر")
    except ImportError:
        print("❌ Pillow مفقود")
        return False
    
    # 3. فحص الملفات الأساسية
    files = [
        'main.py',
        'requirements.txt',
        'api/simple_ayrshare_client.py',
        'utils/config.py',
        'utils/logger.py',
        'database/database.py',
        'gui/main_window.py'
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} مفقود")
            return False
    
    # 4. فحص العميل المخصوص
    try:
        from api.simple_ayrshare_client import SimpleAyrshareClient
        client = SimpleAyrshareClient()
        print("✅ عميل Ayrshare المخصوص يعمل")
    except Exception as e:
        print(f"❌ مشكلة في العميل: {e}")
        return False
    
    # 5. فحص الواجهة الرسومية
    try:
        from gui.main_window import MainWindow
        print("✅ الواجهة الرسومية جاهزة")
    except Exception as e:
        print(f"❌ مشكلة في الواجهة: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 جميع الفحوصات نجحت!")
    print("✅ التطبيق جاهز للتشغيل 100%")
    print("🚀 شغل: python main.py")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = check_all()
    if not success:
        print("\n❌ يوجد مشاكل تحتاج حل")
        input("اضغط Enter للخروج...")
    else:
        print("\n✅ كل شيء مثالي!")
        run_now = input("هل تريد تشغيل التطبيق الآن؟ (y/n): ").strip().lower()
        if run_now == 'y':
            print("🚀 تشغيل التطبيق...")
            os.system("python main.py")
