# -*- coding: utf-8 -*-
"""
تشغيل نظيف للتطبيق بدون أخطاء
"""
import sys
import os

def main():
    """تشغيل التطبيق"""
    print("🚀 Ayrshare Social Media Manager")
    print("=" * 50)
    
    try:
        # إضافة المسار الحالي
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        print("📦 تحميل المكونات...")
        
        # استيراد المكونات الأساسية
        from utils.logger import logger
        from utils.config import config
        from database.database import db
        
        print("✅ تم تحميل المكونات الأساسية")
        
        # إنشاء قواعد البيانات
        db.init_db()
        print("✅ تم إنشاء قواعد البيانات")
        
        # استيراد الواجهة الرسومية
        from gui.main_window import MainWindow
        print("✅ تم تحميل الواجهة الرسومية")
        
        # تشغيل التطبيق
        print("🎯 بدء تشغيل التطبيق...")
        app = MainWindow()
        app.run()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("اضغط Enter للخروج...")
        sys.exit(1)
