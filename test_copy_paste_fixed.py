# -*- coding: utf-8 -*-
"""
اختبار النسخ واللصق المحسن
"""
import tkinter as tk
import customtkinter as ctk

def test_copy_paste_fixed():
    """اختبار النسخ واللصق المحسن"""
    print("🧪 اختبار النسخ واللصق المحسن")
    print("=" * 50)
    
    # إنشاء نافذة اختبار
    root = ctk.CTk()
    root.title("اختبار النسخ واللصق المحسن")
    root.geometry("700x500")
    
    # إنشاء مربع نص للاختبار
    text_frame = ctk.CTkFrame(root)
    text_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    ctk.CTkLabel(text_frame, text="اختبار النسخ واللصق المحسن", 
                font=("Arial", 16, "bold")).pack(pady=10)
    
    # مربع النص الرئيسي
    test_textbox = ctk.CTkTextbox(text_frame, height=200)
    test_textbox.pack(fill="x", padx=10, pady=5)
    
    # إدراج نص تجريبي
    test_content = """🎯 نص تجريبي للنسخ واللصق المحسن

هذا نص تجريبي يحتوي على:
- نصوص عربية وإنجليزية
- أرقام: 123456789
- رموز: !@#$%^&*()
- هاشتاجات: #اختبار #test #نسخ_ولصق

جرب الاختصارات التالية:
• Ctrl+C للنسخ
• Ctrl+V للصق
• Ctrl+X للقص
• Ctrl+A لتحديد الكل
• النقر الأيمن لقائمة السياق

✅ إذا كانت هذه الاختصارات تعمل، فالنسخ واللصق يعمل بنجاح!"""
    
    test_textbox.insert("1.0", test_content)
    
    # حقل الهاشتاجات للاختبار
    hashtags_frame = ctk.CTkFrame(text_frame)
    hashtags_frame.pack(fill="x", padx=10, pady=5)
    
    ctk.CTkLabel(hashtags_frame, text="اختبار الهاشتاجات:", width=150).pack(side="left", padx=5)
    hashtags_var = tk.StringVar(value="#اختبار #test #copy_paste")
    hashtags_entry = ctk.CTkEntry(hashtags_frame, textvariable=hashtags_var)
    hashtags_entry.pack(side="left", padx=5, fill="x", expand=True)
    
    # شريط الحالة
    status_frame = ctk.CTkFrame(text_frame)
    status_frame.pack(fill="x", padx=10, pady=5)
    
    status_label = ctk.CTkLabel(status_frame, text="جاهز للاختبار - جرب الاختصارات!")
    status_label.pack(pady=5)
    
    # وظائف النسخ واللصق المحسنة
    def copy_text(event=None):
        try:
            # للحصول على النص المحدد في CustomTkinter
            selected_text = test_textbox.get("sel.first", "sel.last")
            if selected_text:
                root.clipboard_clear()
                root.clipboard_append(selected_text)
                status_label.configure(text="✅ تم نسخ النص المحدد")
                return "break"
        except tk.TclError:
            # إذا لم يكن هناك نص محدد، انسخ كل النص
            try:
                all_text = test_textbox.get("1.0", "end-1c")
                if all_text.strip():
                    root.clipboard_clear()
                    root.clipboard_append(all_text)
                    status_label.configure(text="✅ تم نسخ كل النص")
            except:
                pass
        return "break"
    
    def paste_text(event=None):
        try:
            clipboard_text = root.clipboard_get()
            if clipboard_text:
                # الحصول على موضع المؤشر الحالي
                cursor_pos = test_textbox.index(tk.INSERT)
                # إدراج النص في موضع المؤشر
                test_textbox.insert(cursor_pos, clipboard_text)
                status_label.configure(text="✅ تم لصق النص")
        except tk.TclError:
            pass
        return "break"
    
    def cut_text(event=None):
        try:
            # الحصول على النص المحدد
            selected_text = test_textbox.get("sel.first", "sel.last")
            if selected_text:
                root.clipboard_clear()
                root.clipboard_append(selected_text)
                # حذف النص المحدد
                test_textbox.delete("sel.first", "sel.last")
                status_label.configure(text="✅ تم قص النص")
        except tk.TclError:
            pass
        return "break"
    
    def select_all_text(event=None):
        test_textbox.tag_add("sel", "1.0", "end-1c")
        test_textbox.mark_set(tk.INSERT, "1.0")
        test_textbox.see(tk.INSERT)
        status_label.configure(text="✅ تم تحديد جميع النص")
        return "break"
    
    def show_context_menu(event):
        try:
            context_menu = tk.Menu(root, tearoff=0)
            context_menu.add_command(label="نسخ (Ctrl+C)", command=copy_text)
            context_menu.add_command(label="لصق (Ctrl+V)", command=paste_text)
            context_menu.add_command(label="قص (Ctrl+X)", command=cut_text)
            context_menu.add_separator()
            context_menu.add_command(label="تحديد الكل (Ctrl+A)", command=select_all_text)
            context_menu.tk_popup(event.x_root, event.y_root)
        except Exception as e:
            print(f"خطأ في قائمة السياق: {e}")
    
    # وظائف النسخ واللصق للهاشتاجات
    def copy_hashtags(event=None):
        try:
            try:
                selected_text = hashtags_entry.selection_get()
                if selected_text:
                    root.clipboard_clear()
                    root.clipboard_append(selected_text)
                    status_label.configure(text="✅ تم نسخ الهاشتاجات المحددة")
                    return "break"
            except:
                all_text = hashtags_entry.get()
                if all_text:
                    root.clipboard_clear()
                    root.clipboard_append(all_text)
                    status_label.configure(text="✅ تم نسخ كل الهاشتاجات")
        except:
            pass
        return "break"
    
    def paste_hashtags(event=None):
        try:
            clipboard_text = root.clipboard_get()
            if clipboard_text:
                cursor_pos = hashtags_entry.index(tk.INSERT)
                hashtags_entry.insert(cursor_pos, clipboard_text)
                status_label.configure(text="✅ تم لصق الهاشتاجات")
        except tk.TclError:
            pass
        return "break"
    
    # ربط الاختصارات للنص الرئيسي
    test_textbox.bind("<Control-c>", copy_text)
    test_textbox.bind("<Control-v>", paste_text)
    test_textbox.bind("<Control-a>", select_all_text)
    test_textbox.bind("<Control-x>", cut_text)
    test_textbox.bind("<Button-3>", show_context_menu)
    
    # ربط الاختصارات للهاشتاجات
    hashtags_entry.bind("<Control-c>", copy_hashtags)
    hashtags_entry.bind("<Control-v>", paste_hashtags)
    
    # أزرار الاختبار
    buttons_frame = ctk.CTkFrame(text_frame)
    buttons_frame.pack(fill="x", padx=10, pady=5)
    
    ctk.CTkButton(buttons_frame, text="اختبار النسخ", 
                 command=copy_text).pack(side="left", padx=5)
    ctk.CTkButton(buttons_frame, text="اختبار اللصق", 
                 command=paste_text).pack(side="left", padx=5)
    ctk.CTkButton(buttons_frame, text="تحديد الكل", 
                 command=select_all_text).pack(side="left", padx=5)
    
    def test_clipboard():
        """اختبار الحافظة"""
        try:
            # نسخ نص تجريبي
            test_text = "نص تجريبي للحافظة"
            root.clipboard_clear()
            root.clipboard_append(test_text)
            
            # قراءة من الحافظة
            clipboard_content = root.clipboard_get()
            if clipboard_content == test_text:
                status_label.configure(text="✅ الحافظة تعمل بشكل صحيح")
            else:
                status_label.configure(text="❌ مشكلة في الحافظة")
        except Exception as e:
            status_label.configure(text=f"❌ خطأ في الحافظة: {e}")
    
    ctk.CTkButton(buttons_frame, text="اختبار الحافظة", 
                 command=test_clipboard).pack(side="left", padx=5)
    
    def close_test():
        print("✅ اختبار النسخ واللصق المحسن مكتمل")
        root.destroy()
    
    ctk.CTkButton(buttons_frame, text="إغلاق الاختبار", 
                 command=close_test).pack(side="right", padx=5)
    
    print("🎯 تعليمات الاختبار المحسن:")
    print("1. حدد نص في المربع الكبير")
    print("2. اضغط Ctrl+C للنسخ")
    print("3. انقر في مكان آخر")
    print("4. اضغط Ctrl+V للصق")
    print("5. جرب النقر الأيمن لقائمة السياق")
    print("6. اختبر حقل الهاشتاجات أيضاً")
    print("7. جرب الأزرار للاختبار اليدوي")
    print("\n✅ إذا عملت الاختصارات، فالنسخ واللصق يعمل!")
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    try:
        test_copy_paste_fixed()
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
