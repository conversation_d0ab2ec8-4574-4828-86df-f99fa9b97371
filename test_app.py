# -*- coding: utf-8 -*-
"""
ملف اختبار البرنامج
"""

import sys
import os

# إضافة المجلد الحالي لمسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد جميع المكونات"""
    print("🔍 اختبار استيراد المكونات...")
    
    try:
        from utils.logger import logger
        print("✅ تم استيراد logger")
        
        from utils.config import config
        print("✅ تم استيراد config")
        
        from database.models import Post, ScheduledPost, RateLimit
        print("✅ تم استيراد models")
        
        from database.database import db
        print("✅ تم استيراد database")
        
        from api.rate_limiter import rate_limiter
        print("✅ تم استيراد rate_limiter")
        
        from api.ayrshare_client import ayrshare_client
        print("✅ تم استيراد ayrshare_client")
        
        print("✅ جميع المكونات تم استيرادها بنجاح!")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from database.database import db
        from database.models import Post
        from datetime import datetime
        
        # إنشاء قاعدة البيانات
        db.init_database()
        print("✅ تم إنشاء قاعدة البيانات")
        
        # إنشاء منشور تجريبي
        test_post = Post(
            title="منشور تجريبي",
            content="هذا منشور تجريبي للاختبار",
            platforms=["facebook", "instagram"],
            hashtags="#اختبار #تجريبي",
            status="draft"
        )
        
        post_id = db.create_post(test_post)
        if post_id:
            print("✅ تم إنشاء منشور تجريبي")
            
            # استرجاع المنشور
            retrieved_post = db.get_post(post_id)
            if retrieved_post:
                print("✅ تم استرجاع المنشور")
                
                # حذف المنشور التجريبي
                if db.delete_post(post_id):
                    print("✅ تم حذف المنشور التجريبي")
                    
        print("✅ اختبار قاعدة البيانات مكتمل!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_config():
    """اختبار نظام الإعدادات"""
    print("\n⚙️ اختبار نظام الإعدادات...")
    
    try:
        from utils.config import config
        
        # اختبار قراءة الإعدادات
        api_key = config.get('api_key', '')
        print(f"✅ قراءة API Key: {'موجود' if api_key else 'غير موجود'}")
        
        # اختبار كتابة الإعدادات
        config.set('test_setting', 'test_value')
        test_value = config.get('test_setting')
        
        if test_value == 'test_value':
            print("✅ كتابة وقراءة الإعدادات تعمل بشكل صحيح")
            
        print("✅ اختبار نظام الإعدادات مكتمل!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {e}")
        return False

def test_rate_limiter():
    """اختبار نظام حدود المعدل"""
    print("\n⏱️ اختبار نظام حدود المعدل...")
    
    try:
        from api.rate_limiter import rate_limiter
        
        # اختبار فحص الحدود
        can_post, reason = rate_limiter.can_post('facebook')
        print(f"✅ فحص حدود Facebook: {'مسموح' if can_post else f'ممنوع - {reason}'}")
        
        # اختبار تسجيل منشور
        rate_limiter.record_post('facebook')
        print("✅ تم تسجيل منشور تجريبي")
        
        # اختبار الإحصائيات
        stats = rate_limiter.get_usage_stats('facebook')
        if stats:
            print(f"✅ إحصائيات Facebook: {stats.get('hourly_used', 0)}/{stats.get('hourly_limit', 0)} ساعي")
            
        print("✅ اختبار نظام حدود المعدل مكتمل!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حدود المعدل: {e}")
        return False

def test_logger():
    """اختبار نظام السجلات"""
    print("\n📝 اختبار نظام السجلات...")
    
    try:
        from utils.logger import logger
        
        # اختبار كتابة السجلات
        logger.info("رسالة اختبار معلومات")
        logger.warning("رسالة اختبار تحذير")
        logger.error("رسالة اختبار خطأ")
        
        print("✅ تم كتابة رسائل السجل")
        
        # التحقق من وجود مجلد السجلات
        if os.path.exists('logs'):
            print("✅ مجلد السجلات موجود")
        else:
            print("⚠️ مجلد السجلات غير موجود")
            
        print("✅ اختبار نظام السجلات مكتمل!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار السجلات: {e}")
        return False

def test_gui_imports():
    """اختبار استيراد الواجهة الرسومية"""
    print("\n🖥️ اختبار استيراد الواجهة الرسومية...")
    
    try:
        import tkinter as tk
        print("✅ تم استيراد tkinter")
        
        import customtkinter as ctk
        print("✅ تم استيراد customtkinter")
        
        from gui.main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        print("✅ اختبار الواجهة الرسومية مكتمل!")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الواجهة الرسومية: {e}")
        print("💡 تأكد من تثبيت customtkinter: pip install customtkinter")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة الرسومية: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار Ayrshare Social Media Manager")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_logger,
        test_database,
        test_rate_limiter,
        test_gui_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للاستخدام")
        print("💡 يمكنك الآن تشغيل البرنامج باستخدام: python main.py")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
