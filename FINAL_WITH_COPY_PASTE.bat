@echo off
chcp 65001 >nul
title 🚀 Ayrshare Social Media Manager - النسخ واللصق يعمل!

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║           🚀 Ayrshare Social Media Manager 🚀                ║
echo ║                                                              ║
echo ║              برنامج إدارة وسائل التواصل الاجتماعي           ║
echo ║                النسخ واللصق يعمل بشكل مثالي!                ║
echo ║                                                              ║
echo ║    📘 Facebook  📸 Instagram  🎵 TikTok  🐦 Twitter/X        ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ✅ تم إصلاح النسخ واللصق نهائياً
echo ✅ يعمل في مربع النص الرئيسي
echo ✅ يعمل في حقل الهاشتاجات
echo ✅ قائمة السياق بالنقر الأيمن
echo ✅ جميع الاختصارات تعمل بشكل مثالي
echo.
echo ⌨️ الاختصارات المتاحة:
echo • Ctrl+C = نسخ النص المحدد (أو كل النص)
echo • Ctrl+V = لصق النص في موضع المؤشر
echo • Ctrl+X = قص النص المحدد (أو كل النص)
echo • Ctrl+A = تحديد جميع النص
echo • النقر الأيمن = قائمة السياق
echo.
echo 📋 ميزات النسخ واللصق:
echo ✅ نسخ من أي تطبيق آخر واللصق هنا
echo ✅ نسخ من هنا ولصق في أي تطبيق آخر
echo ✅ رسائل تأكيد في شريط الحالة
echo ✅ معالجة ذكية عند عدم وجود تحديد
echo.
echo 🔄 تشغيل التطبيق مع النسخ واللصق المحسن...
echo.

REM تشغيل التطبيق
python main.py

REM في حالة الخطأ
if errorlevel 1 (
    echo.
    echo ❌ خطأ في التشغيل
    echo.
    echo 🧪 هل تريد اختبار النسخ واللصق؟
    set /p test_choice="اكتب 'y' لاختبار النسخ واللصق: "
    if /i "%test_choice%"=="y" (
        echo 🧪 تشغيل اختبار النسخ واللصق المحسن...
        python test_copy_paste_fixed.py
    )
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق التطبيق بنجاح
    echo 📋 النسخ واللصق يعمل بشكل مثالي!
    echo 🎉 استمتع بالاستخدام!
    echo.
)
