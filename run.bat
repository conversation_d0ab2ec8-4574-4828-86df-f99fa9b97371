@echo off
chcp 65001 >nul
title Ayrshare Social Media Manager

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║           🚀 Ayrshare Social Media Manager 🚀                ║
echo ║                                                              ║
echo ║              برنامج إدارة وسائل التواصل الاجتماعي              ║
echo ║                                                              ║
echo ║    📱 Facebook  📸 Instagram  🎵 TikTok                      ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.10 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo تم العثور على Python...

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: pip غير متوفر
    echo يرجى إعادة تثبيت Python مع pip
    pause
    exit /b 1
)

echo تم العثور على pip...

REM التحقق من وجود ملف المتطلبات
if not exist "requirements.txt" (
    echo خطأ: ملف requirements.txt غير موجود
    echo يرجى التأكد من وجود الملف في نفس مجلد البرنامج
    pause
    exit /b 1
)

echo جاري فحص المتطلبات...

REM اختبار بسيط أولاً
python simple_test.py

if errorlevel 1 (
    echo.
    echo فشل الاختبار البسيط
    echo جاري تثبيت المتطلبات...

    REM تثبيت المتطلبات
    pip install -r requirements.txt --quiet

    if errorlevel 1 (
        echo.
        echo خطأ في تثبيت المتطلبات
        echo جاري المحاولة مرة أخرى...
        pip install -r requirements.txt
        if errorlevel 1 (
            echo فشل في تثبيت المتطلبات
            echo يرجى تشغيل الأمر التالي يدوياً:
            echo pip install -r requirements.txt
            pause
            exit /b 1
        )
    )

    echo تم تثبيت المتطلبات، جاري إعادة الاختبار...
    python simple_test.py
)

echo تم تثبيت المتطلبات بنجاح...

REM التحقق من وجود الملف الرئيسي
if not exist "main.py" (
    echo خطأ: ملف main.py غير موجود
    echo يرجى التأكد من وجود الملف في نفس مجلد البرنامج
    pause
    exit /b 1
)

echo.
echo جاري تشغيل البرنامج...
echo.

REM تشغيل البرنامج المحسن
python start.py

REM التحقق من حالة الخروج
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل البرنامج
    echo.
    echo 🔧 نصائح لحل المشكلة:
    echo 1. تأكد من تثبيت Python 3.8+
    echo 2. شغل: pip install -r requirements.txt
    echo 3. راجع ملفات السجل في مجلد logs/
    echo 4. شغل الاختبار: python simple_test.py
    echo.
)

echo.
echo 👋 تم إغلاق البرنامج
echo شكراً لاستخدام Ayrshare Social Media Manager!
pause
