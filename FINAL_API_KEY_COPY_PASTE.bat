@echo off
chcp 65001 >nul
title 🔑 Ayrshare Social Media Manager - النسخ واللصق في حقل API Key

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║           🚀 Ayrshare Social Media Manager 🚀                ║
echo ║                                                              ║
echo ║              برنامج إدارة وسائل التواصل الاجتماعي           ║
echo ║               النسخ واللصق يعمل في حقل API Key!              ║
echo ║                                                              ║
echo ║    📘 Facebook  📸 Instagram  🎵 TikTok  🐦 Twitter/X        ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎉 تم تفعيل النسخ واللصق في جميع الحقول:
echo ✅ حقل API Key - نسخ ولصق كامل
echo ✅ حقل Profile Key - نسخ ولصق كامل
echo ✅ مربع النص الرئيسي - نسخ ولصق كامل
echo ✅ حقل الهاشتاجات - نسخ ولصق كامل
echo ✅ قائمة السياق بالنقر الأيمن
echo ✅ يعمل حتى بعد تغيير API Key
echo.
echo 🔑 ميزات حقل API Key:
echo • نسخ API Key من موقع Ayrshare ولصقه مباشرة
echo • Ctrl+C = نسخ API Key المحدد أو كامل
echo • Ctrl+V = لصق API Key في موضع المؤشر
echo • Ctrl+X = قص API Key المحدد أو كامل
echo • Ctrl+A = تحديد كل API Key
echo • النقر الأيمن = قائمة السياق كاملة
echo • رسائل تأكيد مخصصة في شريط الحالة
echo.
echo ⌨️ الاختصارات المتاحة في جميع الحقول:
echo • Ctrl+C = نسخ النص المحدد (أو كل النص)
echo • Ctrl+V = لصق النص في موضع المؤشر
echo • Ctrl+X = قص النص المحدد (أو كل النص)
echo • Ctrl+A = تحديد جميع النص
echo • النقر الأيمن = قائمة السياق
echo.
echo 🚀 سيناريو الاستخدام:
echo 1. اذهب إلى: https://app.ayrshare.com
echo 2. انسخ API Key من الموقع (Ctrl+C)
echo 3. ارجع للتطبيق
echo 4. انقر في حقل API Key
echo 5. الصق API Key (Ctrl+V)
echo 6. اضغط "اختبار" للتأكد من الاتصال
echo 7. ابدأ النشر على المنصات!
echo.
echo 🔄 تشغيل التطبيق مع النسخ واللصق الكامل...
echo.

REM تشغيل التطبيق
python main.py

REM في حالة الخطأ
if errorlevel 1 (
    echo.
    echo ❌ خطأ في التشغيل
    echo.
    echo 🧪 اختبارات متاحة:
    echo 1. اختبار حقل API Key: python test_api_key_copy_paste.py
    echo 2. اختبار النسخ واللصق العام: python test_copy_paste_fixed.py
    echo 3. اختبار API والنسخ واللصق: python test_api_copy_paste.py
    echo.
    echo 📚 الوثائق المتاحة:
    echo - API_KEY_COPY_PASTE_GUIDE.md: دليل النسخ واللصق في حقل API Key
    echo - API_COPY_PASTE_FIX.md: حل مشكلة النسخ واللصق بعد تغيير API Key
    echo - COPY_PASTE_WORKING_GUIDE.md: دليل النسخ واللصق الشامل
    echo - NO_LIBRARY_GUIDE.md: دليل العميل المخصوص
    echo - AYRSHARE_GUIDE.md: دليل Ayrshare API
    echo - README.md: دليل شامل
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق التطبيق بنجاح
    echo 🔑 النسخ واللصق يعمل في حقل API Key!
    echo 📋 النسخ واللصق يعمل في جميع الحقول!
    echo 🎉 استمتع بسهولة إدخال البيانات!
    echo.
)
