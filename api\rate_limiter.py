# -*- coding: utf-8 -*-
"""
نظام إدارة حدود المعدل وتخطي القيود
"""
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
from utils.logger import logger
from utils.config import config

@dataclass
class PlatformLimits:
    """حدود المنصة"""
    posts_per_hour: int
    posts_per_day: int
    reels_per_day: int = 0
    stories_per_day: int = 0

class RateLimiter:
    """مدير حدود المعدل"""
    
    def __init__(self):
        self.platform_limits = {
            'facebook': PlatformLimits(
                posts_per_hour=config.get('rate_limits.facebook.posts_per_hour', 25),
                posts_per_day=config.get('rate_limits.facebook.posts_per_day', 200),
                reels_per_day=config.get('rate_limits.facebook.reels_per_day', 30)
            ),
            'instagram': PlatformLimits(
                posts_per_hour=config.get('rate_limits.instagram.posts_per_hour', 25),
                posts_per_day=config.get('rate_limits.instagram.posts_per_day', 200),
                stories_per_day=config.get('rate_limits.instagram.stories_per_day', 100)
            ),
            'tiktok': PlatformLimits(
                posts_per_hour=config.get('rate_limits.tiktok.posts_per_hour', 10),
                posts_per_day=config.get('rate_limits.tiktok.posts_per_day', 50)
            )
        }
        
        # تتبع الاستخدام الحالي
        self.usage_tracker = {
            'facebook': {'hourly': [], 'daily': [], 'reels_daily': []},
            'instagram': {'hourly': [], 'daily': [], 'stories_daily': []},
            'tiktok': {'hourly': [], 'daily': []}
        }
        
        # إعدادات تخطي الحدود
        self.bypass_settings = {
            'enabled': config.get('rate_limits.bypass_enabled', False),
            'delay_between_posts': config.get('rate_limits.bypass_delay', 60),  # ثانية
            'max_retries': config.get('rate_limits.max_retries', 3),
            'retry_delay': config.get('rate_limits.retry_delay', 300)  # 5 دقائق
        }
    
    def can_post(self, platform: str, post_type: str = 'post') -> Tuple[bool, Optional[str]]:
        """فحص إمكانية النشر على المنصة"""
        try:
            if platform not in self.platform_limits:
                return False, f"منصة غير مدعومة: {platform}"
            
            # تنظيف البيانات القديمة
            self._cleanup_old_usage(platform)
            
            limits = self.platform_limits[platform]
            usage = self.usage_tracker[platform]
            
            # فحص الحد الساعي
            hourly_count = len(usage['hourly'])
            if hourly_count >= limits.posts_per_hour:
                if self.bypass_settings['enabled']:
                    logger.warning(f"تخطي الحد الساعي لـ {platform}: {hourly_count}/{limits.posts_per_hour}")
                else:
                    return False, f"تم الوصول للحد الساعي: {hourly_count}/{limits.posts_per_hour}"
            
            # فحص الحد اليومي
            daily_count = len(usage['daily'])
            if daily_count >= limits.posts_per_day:
                if self.bypass_settings['enabled']:
                    logger.warning(f"تخطي الحد اليومي لـ {platform}: {daily_count}/{limits.posts_per_day}")
                else:
                    return False, f"تم الوصول للحد اليومي: {daily_count}/{limits.posts_per_day}"
            
            # فحص حدود خاصة بنوع المنشور
            if post_type == 'reels' and platform == 'facebook':
                reels_count = len(usage.get('reels_daily', []))
                if reels_count >= limits.reels_per_day:
                    if self.bypass_settings['enabled']:
                        logger.warning(f"تخطي حد الريلز اليومي لـ {platform}: {reels_count}/{limits.reels_per_day}")
                    else:
                        return False, f"تم الوصول لحد الريلز اليومي: {reels_count}/{limits.reels_per_day}"
            
            elif post_type == 'stories' and platform == 'instagram':
                stories_count = len(usage.get('stories_daily', []))
                if stories_count >= limits.stories_per_day:
                    if self.bypass_settings['enabled']:
                        logger.warning(f"تخطي حد الستوريز اليومي لـ {platform}: {stories_count}/{limits.stories_per_day}")
                    else:
                        return False, f"تم الوصول لحد الستوريز اليومي: {stories_count}/{limits.stories_per_day}"
            
            return True, None
            
        except Exception as e:
            logger.error(f"خطأ في فحص حدود المعدل: {e}")
            return False, f"خطأ في فحص الحدود: {e}"
    
    def record_post(self, platform: str, post_type: str = 'post') -> None:
        """تسجيل منشور جديد"""
        try:
            current_time = datetime.now()
            
            if platform in self.usage_tracker:
                usage = self.usage_tracker[platform]
                
                # تسجيل في الاستخدام الساعي واليومي
                usage['hourly'].append(current_time)
                usage['daily'].append(current_time)
                
                # تسجيل في الاستخدام الخاص بنوع المنشور
                if post_type == 'reels' and platform == 'facebook':
                    usage['reels_daily'].append(current_time)
                elif post_type == 'stories' and platform == 'instagram':
                    usage['stories_daily'].append(current_time)
                
                logger.info(f"تم تسجيل منشور {post_type} على {platform}")
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل المنشور: {e}")
    
    def _cleanup_old_usage(self, platform: str) -> None:
        """تنظيف بيانات الاستخدام القديمة"""
        try:
            current_time = datetime.now()
            hour_ago = current_time - timedelta(hours=1)
            day_ago = current_time - timedelta(days=1)
            
            usage = self.usage_tracker[platform]
            
            # تنظيف البيانات الساعية
            usage['hourly'] = [t for t in usage['hourly'] if t > hour_ago]
            
            # تنظيف البيانات اليومية
            usage['daily'] = [t for t in usage['daily'] if t > day_ago]
            
            # تنظيف البيانات الخاصة
            if 'reels_daily' in usage:
                usage['reels_daily'] = [t for t in usage['reels_daily'] if t > day_ago]
            
            if 'stories_daily' in usage:
                usage['stories_daily'] = [t for t in usage['stories_daily'] if t > day_ago]
                
        except Exception as e:
            logger.error(f"خطأ في تنظيف بيانات الاستخدام: {e}")
    
    def get_usage_stats(self, platform: str) -> Dict[str, int]:
        """الحصول على إحصائيات الاستخدام"""
        try:
            self._cleanup_old_usage(platform)
            usage = self.usage_tracker[platform]
            limits = self.platform_limits[platform]
            
            stats = {
                'hourly_used': len(usage['hourly']),
                'hourly_limit': limits.posts_per_hour,
                'daily_used': len(usage['daily']),
                'daily_limit': limits.posts_per_day
            }
            
            if platform == 'facebook' and 'reels_daily' in usage:
                stats['reels_daily_used'] = len(usage['reels_daily'])
                stats['reels_daily_limit'] = limits.reels_per_day
            
            if platform == 'instagram' and 'stories_daily' in usage:
                stats['stories_daily_used'] = len(usage['stories_daily'])
                stats['stories_daily_limit'] = limits.stories_per_day
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الاستخدام: {e}")
            return {}
    
    def get_next_available_time(self, platform: str, post_type: str = 'post') -> Optional[datetime]:
        """الحصول على الوقت التالي المتاح للنشر"""
        try:
            can_post, reason = self.can_post(platform, post_type)
            if can_post:
                return datetime.now()
            
            # حساب الوقت التالي بناءً على نوع الحد
            usage = self.usage_tracker[platform]
            
            if 'ساعي' in reason:
                # العثور على أقدم منشور في الساعة الماضية
                if usage['hourly']:
                    oldest_post = min(usage['hourly'])
                    return oldest_post + timedelta(hours=1)
            
            elif 'يومي' in reason:
                # العثور على أقدم منشور في اليوم الماضي
                if usage['daily']:
                    oldest_post = min(usage['daily'])
                    return oldest_post + timedelta(days=1)
            
            return datetime.now() + timedelta(hours=1)  # افتراضي
            
        except Exception as e:
            logger.error(f"خطأ في حساب الوقت التالي المتاح: {e}")
            return None
    
    def enable_bypass(self, enabled: bool = True) -> None:
        """تفعيل أو إلغاء تفعيل تخطي الحدود"""
        self.bypass_settings['enabled'] = enabled
        config.set('rate_limits.bypass_enabled', enabled)
        logger.info(f"تخطي الحدود: {'مفعل' if enabled else 'معطل'}")
    
    def set_bypass_delay(self, delay_seconds: int) -> None:
        """تعيين التأخير بين المنشورات عند تخطي الحدود"""
        self.bypass_settings['delay_between_posts'] = delay_seconds
        config.set('rate_limits.bypass_delay', delay_seconds)
        logger.info(f"تم تعيين تأخير تخطي الحدود: {delay_seconds} ثانية")
    
    def wait_if_needed(self, platform: str, post_type: str = 'post') -> bool:
        """انتظار إذا كان مطلوباً قبل النشر"""
        try:
            if not self.bypass_settings['enabled']:
                return True
            
            # انتظار التأخير المحدد بين المنشورات
            delay = self.bypass_settings['delay_between_posts']
            if delay > 0:
                logger.info(f"انتظار {delay} ثانية قبل النشر على {platform}")
                time.sleep(delay)
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في الانتظار: {e}")
            return False

# إنشاء مثيل عام لمدير الحدود
rate_limiter = RateLimiter()
