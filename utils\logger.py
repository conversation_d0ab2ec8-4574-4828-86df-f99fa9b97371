# -*- coding: utf-8 -*-
"""
نظام السجلات والتتبع
"""
import logging
import os
from datetime import datetime
from typing import Optional

class Logger:
    """مدير السجلات للتطبيق"""
    
    def __init__(self, name: str = "<PERSON>yr<PERSON><PERSON>App", log_dir: str = "logs"):
        self.name = name
        self.log_dir = log_dir
        self.logger = None
        self.setup_logger()
    
    def setup_logger(self) -> None:
        """إعداد نظام السجلات"""
        # إنشاء مجلد السجلات إذا لم يكن موجوداً
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        
        # إنشاء اسم ملف السجل بالتاريخ
        log_filename = f"{self.name}_{datetime.now().strftime('%Y%m%d')}.log"
        log_path = os.path.join(self.log_dir, log_filename)
        
        # إعداد المسجل
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.DEBUG)
        
        # تجنب إضافة معالجات متعددة
        if not self.logger.handlers:
            # معالج الملف
            file_handler = logging.FileHandler(log_path, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            
            # معالج وحدة التحكم
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # تنسيق السجلات
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def debug(self, message: str, **kwargs) -> None:
        """تسجيل رسالة تصحيح"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs) -> None:
        """تسجيل رسالة معلومات"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """تسجيل رسالة تحذير"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """تسجيل رسالة خطأ"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """تسجيل رسالة خطأ حرج"""
        self.logger.critical(message, **kwargs)
    
    def log_api_request(self, method: str, url: str, status_code: Optional[int] = None, 
                       response_time: Optional[float] = None) -> None:
        """تسجيل طلب API"""
        message = f"API Request: {method} {url}"
        if status_code:
            message += f" - Status: {status_code}"
        if response_time:
            message += f" - Time: {response_time:.2f}s"
        self.info(message)
    
    def log_post_attempt(self, platforms: list, post_id: Optional[str] = None, 
                        success: bool = True, error: Optional[str] = None) -> None:
        """تسجيل محاولة نشر"""
        platforms_str = ", ".join(platforms)
        if success:
            message = f"نشر ناجح على المنصات: {platforms_str}"
            if post_id:
                message += f" - معرف المنشور: {post_id}"
            self.info(message)
        else:
            message = f"فشل النشر على المنصات: {platforms_str}"
            if error:
                message += f" - الخطأ: {error}"
            self.error(message)
    
    def log_rate_limit(self, platform: str, limit_type: str, current_count: int, 
                      max_count: int) -> None:
        """تسجيل حالة حدود المعدل"""
        message = f"حد المعدل - {platform}: {limit_type} ({current_count}/{max_count})"
        if current_count >= max_count:
            self.warning(f"تم الوصول للحد الأقصى - {message}")
        else:
            self.info(message)
    
    def log_scheduler_event(self, event_type: str, post_id: Optional[str] = None, 
                           scheduled_time: Optional[datetime] = None) -> None:
        """تسجيل أحداث الجدولة"""
        message = f"جدولة: {event_type}"
        if post_id:
            message += f" - معرف المنشور: {post_id}"
        if scheduled_time:
            message += f" - الوقت المجدول: {scheduled_time.strftime('%Y-%m-%d %H:%M:%S')}"
        self.info(message)
    
    def clear_old_logs(self, days_to_keep: int = 30) -> None:
        """حذف السجلات القديمة"""
        try:
            current_time = datetime.now()
            for filename in os.listdir(self.log_dir):
                if filename.endswith('.log'):
                    file_path = os.path.join(self.log_dir, filename)
                    file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    if (current_time - file_time).days > days_to_keep:
                        os.remove(file_path)
                        self.info(f"تم حذف السجل القديم: {filename}")
        except Exception as e:
            self.error(f"خطأ في حذف السجلات القديمة: {e}")

# إنشاء مثيل عام للمسجل
logger = Logger()
