# -*- coding: utf-8 -*-
"""
اختبار النسخ واللصق في حقل API Key
"""
import tkinter as tk
import customtkinter as ctk

def test_api_key_copy_paste():
    """اختبار النسخ واللصق في حقل API Key"""
    print("🧪 اختبار النسخ واللصق في حقل API Key")
    print("=" * 50)
    
    # إنشاء نافذة اختبار
    root = ctk.CTk()
    root.title("اختبار النسخ واللصق في حقل API Key")
    root.geometry("800x500")
    
    # إنشاء الإطار الرئيسي
    main_frame = ctk.CTkFrame(root)
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    ctk.CTkLabel(main_frame, text="اختبار النسخ واللصق في حقل API Key", 
                font=("Arial", 18, "bold")).pack(pady=10)
    
    # إطار API Key
    api_frame = ctk.CTkFrame(main_frame)
    api_frame.pack(fill="x", padx=10, pady=10)
    
    ctk.CTkLabel(api_frame, text="حقل API Key مع النسخ واللصق", 
                font=("Arial", 14, "bold")).pack(pady=5)
    
    # حقل API Key
    api_key_frame = ctk.CTkFrame(api_frame)
    api_key_frame.pack(fill="x", padx=10, pady=5)
    
    ctk.CTkLabel(api_key_frame, text="API Key:", width=100).pack(side="left", padx=5)
    api_key_var = tk.StringVar(value="test_api_key_12345_copy_paste")
    api_key_entry = ctk.CTkEntry(api_key_frame, textvariable=api_key_var, width=400, show="*")
    api_key_entry.pack(side="left", padx=5, fill="x", expand=True)
    
    # حقل Profile Key
    profile_key_frame = ctk.CTkFrame(api_frame)
    profile_key_frame.pack(fill="x", padx=10, pady=5)
    
    ctk.CTkLabel(profile_key_frame, text="Profile Key:", width=100).pack(side="left", padx=5)
    profile_key_var = tk.StringVar(value="test_profile_key_67890")
    profile_key_entry = ctk.CTkEntry(profile_key_frame, textvariable=profile_key_var, width=400)
    profile_key_entry.pack(side="left", padx=5, fill="x", expand=True)
    
    # شريط الحالة
    status_frame = ctk.CTkFrame(main_frame)
    status_frame.pack(fill="x", padx=10, pady=5)
    
    status_label = ctk.CTkLabel(status_frame, text="جاهز للاختبار - جرب النسخ واللصق في حقول API Key!")
    status_label.pack(pady=5)
    
    # وظائف النسخ واللصق لـ API Key
    def copy_api_key(event=None):
        try:
            try:
                selected_text = api_key_entry.selection_get()
                if selected_text:
                    root.clipboard_clear()
                    root.clipboard_append(selected_text)
                    status_label.configure(text="✅ تم نسخ API Key المحدد")
                    return "break"
            except:
                all_text = api_key_entry.get()
                if all_text:
                    root.clipboard_clear()
                    root.clipboard_append(all_text)
                    status_label.configure(text="✅ تم نسخ كل API Key")
        except:
            pass
        return "break"
    
    def paste_api_key(event=None):
        try:
            clipboard_text = root.clipboard_get()
            if clipboard_text:
                cursor_pos = api_key_entry.index(tk.INSERT)
                api_key_entry.insert(cursor_pos, clipboard_text)
                status_label.configure(text="✅ تم لصق API Key")
        except tk.TclError:
            pass
        return "break"
    
    def cut_api_key(event=None):
        try:
            try:
                selected_text = api_key_entry.selection_get()
                if selected_text:
                    root.clipboard_clear()
                    root.clipboard_append(selected_text)
                    api_key_entry.delete(tk.SEL_FIRST, tk.SEL_LAST)
                    status_label.configure(text="✅ تم قص API Key المحدد")
            except:
                all_text = api_key_entry.get()
                if all_text:
                    root.clipboard_clear()
                    root.clipboard_append(all_text)
                    api_key_entry.delete(0, tk.END)
                    status_label.configure(text="✅ تم قص كل API Key")
        except:
            pass
        return "break"
    
    def select_all_api_key(event=None):
        try:
            api_key_entry.select_range(0, tk.END)
            api_key_entry.icursor(tk.END)
            status_label.configure(text="✅ تم تحديد كل API Key")
        except:
            pass
        return "break"
    
    def show_api_key_context_menu(event):
        try:
            context_menu = tk.Menu(root, tearoff=0)
            context_menu.add_command(label="نسخ (Ctrl+C)", command=copy_api_key)
            context_menu.add_command(label="لصق (Ctrl+V)", command=paste_api_key)
            context_menu.add_command(label="قص (Ctrl+X)", command=cut_api_key)
            context_menu.add_separator()
            context_menu.add_command(label="تحديد الكل (Ctrl+A)", command=select_all_api_key)
            context_menu.add_separator()
            context_menu.add_command(label="مسح API Key", command=lambda: api_key_entry.delete(0, tk.END))
            context_menu.tk_popup(event.x_root, event.y_root)
        except Exception as e:
            print(f"خطأ في قائمة السياق: {e}")
    
    # وظائف النسخ واللصق لـ Profile Key
    def copy_profile_key(event=None):
        try:
            try:
                selected_text = profile_key_entry.selection_get()
                if selected_text:
                    root.clipboard_clear()
                    root.clipboard_append(selected_text)
                    status_label.configure(text="✅ تم نسخ Profile Key المحدد")
                    return "break"
            except:
                all_text = profile_key_entry.get()
                if all_text:
                    root.clipboard_clear()
                    root.clipboard_append(all_text)
                    status_label.configure(text="✅ تم نسخ كل Profile Key")
        except:
            pass
        return "break"
    
    def paste_profile_key(event=None):
        try:
            clipboard_text = root.clipboard_get()
            if clipboard_text:
                cursor_pos = profile_key_entry.index(tk.INSERT)
                profile_key_entry.insert(cursor_pos, clipboard_text)
                status_label.configure(text="✅ تم لصق Profile Key")
        except tk.TclError:
            pass
        return "break"
    
    def cut_profile_key(event=None):
        try:
            try:
                selected_text = profile_key_entry.selection_get()
                if selected_text:
                    root.clipboard_clear()
                    root.clipboard_append(selected_text)
                    profile_key_entry.delete(tk.SEL_FIRST, tk.SEL_LAST)
                    status_label.configure(text="✅ تم قص Profile Key المحدد")
            except:
                all_text = profile_key_entry.get()
                if all_text:
                    root.clipboard_clear()
                    root.clipboard_append(all_text)
                    profile_key_entry.delete(0, tk.END)
                    status_label.configure(text="✅ تم قص كل Profile Key")
        except:
            pass
        return "break"
    
    def select_all_profile_key(event=None):
        try:
            profile_key_entry.select_range(0, tk.END)
            profile_key_entry.icursor(tk.END)
            status_label.configure(text="✅ تم تحديد كل Profile Key")
        except:
            pass
        return "break"
    
    # ربط الاختصارات لـ API Key
    api_key_entry.bind("<Control-c>", copy_api_key)
    api_key_entry.bind("<Control-v>", paste_api_key)
    api_key_entry.bind("<Control-a>", select_all_api_key)
    api_key_entry.bind("<Control-x>", cut_api_key)
    api_key_entry.bind("<Button-3>", show_api_key_context_menu)
    
    # ربط الاختصارات لـ Profile Key
    profile_key_entry.bind("<Control-c>", copy_profile_key)
    profile_key_entry.bind("<Control-v>", paste_profile_key)
    profile_key_entry.bind("<Control-a>", select_all_profile_key)
    profile_key_entry.bind("<Control-x>", cut_profile_key)
    
    # أزرار الاختبار
    buttons_frame = ctk.CTkFrame(main_frame)
    buttons_frame.pack(fill="x", padx=10, pady=10)
    
    ctk.CTkButton(buttons_frame, text="نسخ API Key", 
                 command=copy_api_key).pack(side="left", padx=5)
    ctk.CTkButton(buttons_frame, text="لصق API Key", 
                 command=paste_api_key).pack(side="left", padx=5)
    ctk.CTkButton(buttons_frame, text="تحديد كل API Key", 
                 command=select_all_api_key).pack(side="left", padx=5)
    
    def test_clipboard():
        """اختبار الحافظة"""
        try:
            test_text = "test_api_key_clipboard_12345"
            root.clipboard_clear()
            root.clipboard_append(test_text)
            
            clipboard_content = root.clipboard_get()
            if clipboard_content == test_text:
                status_label.configure(text="✅ الحافظة تعمل بشكل صحيح")
            else:
                status_label.configure(text="❌ مشكلة في الحافظة")
        except Exception as e:
            status_label.configure(text=f"❌ خطأ في الحافظة: {e}")
    
    ctk.CTkButton(buttons_frame, text="اختبار الحافظة", 
                 command=test_clipboard).pack(side="left", padx=5)
    
    def clear_all():
        """مسح جميع الحقول"""
        api_key_entry.delete(0, tk.END)
        profile_key_entry.delete(0, tk.END)
        status_label.configure(text="تم مسح جميع الحقول")
    
    ctk.CTkButton(buttons_frame, text="مسح الكل", 
                 command=clear_all).pack(side="left", padx=5)
    
    def close_test():
        print("✅ اختبار النسخ واللصق في حقل API Key مكتمل")
        root.destroy()
    
    ctk.CTkButton(buttons_frame, text="إغلاق الاختبار", 
                 command=close_test).pack(side="right", padx=5)
    
    # معلومات الاختبار
    info_frame = ctk.CTkFrame(main_frame)
    info_frame.pack(fill="x", padx=10, pady=5)
    
    info_text = """🎯 تعليمات الاختبار:
1. حدد نص في حقل API Key أو Profile Key
2. اضغط Ctrl+C للنسخ
3. انقر في حقل آخر
4. اضغط Ctrl+V للصق
5. جرب النقر الأيمن لقائمة السياق
6. جرب Ctrl+A لتحديد الكل
7. جرب Ctrl+X للقص

✅ إذا عملت الاختصارات، فالنسخ واللصق يعمل في حقل API Key!"""
    
    ctk.CTkLabel(info_frame, text=info_text, justify="left").pack(pady=10)
    
    print("🎯 تعليمات الاختبار:")
    print("1. حدد نص في حقل API Key")
    print("2. اضغط Ctrl+C للنسخ")
    print("3. انقر في مكان آخر")
    print("4. اضغط Ctrl+V للصق")
    print("5. جرب النقر الأيمن لقائمة السياق")
    print("6. جرب جميع الاختصارات")
    print("\n✅ إذا عملت الاختصارات، فالنسخ واللصق يعمل!")
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    try:
        test_api_key_copy_paste()
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
