# -*- coding: utf-8 -*-
"""
عميل Ayrshare API
"""
import time
import requests
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from ayrshare import SocialPost
from utils.logger import logger
from utils.config import config
from .rate_limiter import rate_limiter

class AyrshareClient:
    """عميل للتعامل مع Ayrshare API"""

    def __init__(self, api_key: Optional[str] = None, profile_key: Optional[str] = None):
        self.api_key = api_key or config.get('api_key', '')
        self.profile_key = profile_key or config.get('profile_key', '')
        self.client = None
        self.base_url = "https://app.ayrshare.com/api"

        if self.api_key:
            self.client = SocialPost(self.api_key)

    def set_credentials(self, api_key: str, profile_key: Optional[str] = None) -> bool:
        """تعيين بيانات الاعتماد"""
        try:
            self.api_key = api_key
            self.profile_key = profile_key or self.profile_key
            self.client = SocialPost(api_key)

            # حفظ في الإعدادات
            config.set('api_key', api_key)
            if profile_key:
                config.set('profile_key', profile_key)

            logger.info("تم تعيين بيانات الاعتماد بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في تعيين بيانات الاعتماد: {e}")
            return False

    def test_connection(self) -> tuple[bool, str]:
        """اختبار الاتصال مع API"""
        try:
            if not self.client:
                return False, "لم يتم تعيين API Key"

            # اختبار بسيط للحصول على الملف الشخصي
            result = self.client.user()

            if result.get('status') == 'success':
                logger.info("تم اختبار الاتصال بنجاح")
                return True, "الاتصال ناجح"
            else:
                error_msg = result.get('message', 'خطأ غير معروف')
                logger.error(f"فشل اختبار الاتصال: {error_msg}")
                return False, error_msg

        except Exception as e:
            logger.error(f"خطأ في اختبار الاتصال: {e}")
            return False, f"خطأ في الاتصال: {e}"

    def post_content(self, content: str, platforms: List[str],
                    media_urls: Optional[List[str]] = None,
                    hashtags: Optional[str] = None,
                    scheduled_time: Optional[datetime] = None,
                    post_options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """نشر محتوى على المنصات المحددة"""
        try:
            if not self.client:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            # التحقق من حدود المعدل لكل منصة
            for platform in platforms:
                can_post, reason = rate_limiter.can_post(platform)
                if not can_post and not rate_limiter.bypass_settings['enabled']:
                    return {'status': 'error', 'message': f'{platform}: {reason}'}

            # إعداد البيانات للنشر
            post_data = {
                'post': content,
                'platforms': platforms
            }

            # إضافة الملف الشخصي إذا كان متوفراً
            if self.profile_key:
                post_data['profileKey'] = self.profile_key

            # إضافة الوسائط
            if media_urls:
                post_data['mediaUrls'] = media_urls

            # إضافة الهاشتاجات للمحتوى
            if hashtags:
                post_data['post'] = f"{content} {hashtags}"

            # إضافة الجدولة
            if scheduled_time:
                post_data['scheduleDate'] = scheduled_time.isoformat()

            # إضافة خيارات خاصة بالمنصات
            if post_options:
                post_data.update(post_options)

            # انتظار إذا كان تخطي الحدود مفعلاً
            for platform in platforms:
                rate_limiter.wait_if_needed(platform)

            # إرسال الطلب
            start_time = time.time()
            result = self.client.post(post_data)
            response_time = time.time() - start_time

            # تسجيل الطلب
            logger.log_api_request('POST', '/post',
                                 result.get('status'), response_time)

            if result.get('status') == 'success':
                # تسجيل النشر الناجح في مدير الحدود
                for platform in platforms:
                    rate_limiter.record_post(platform)

                logger.log_post_attempt(platforms, result.get('id'), True)
                return result
            else:
                error_msg = result.get('message', 'خطأ غير معروف')
                logger.log_post_attempt(platforms, None, False, error_msg)
                return result

        except Exception as e:
            logger.error(f"خطأ في النشر: {e}")
            return {'status': 'error', 'message': f'خطأ في النشر: {e}'}

    def post_video(self, content: str, video_path: str, platforms: List[str],
                  hashtags: Optional[str] = None,
                  video_options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """نشر فيديو"""
        try:
            # رفع الفيديو أولاً
            media_result = self.upload_media(video_path)
            if media_result.get('status') != 'success':
                return media_result

            media_url = media_result.get('url')
            if not media_url:
                return {'status': 'error', 'message': 'فشل في الحصول على رابط الفيديو'}

            # إعداد خيارات الفيديو
            post_options = {}

            # خيارات Facebook Reels
            if 'facebook' in platforms and video_options:
                if video_options.get('is_reel'):
                    post_options['faceBookOptions'] = {
                        'reels': True,
                        'title': video_options.get('title', ''),
                        'thumbNail': video_options.get('thumbnail_url', '')
                    }

            # خيارات Instagram
            if 'instagram' in platforms and video_options:
                if video_options.get('is_reel'):
                    post_options['instagramOptions'] = {
                        'reels': True
                    }

            # نشر الفيديو
            return self.post_content(
                content=content,
                platforms=platforms,
                media_urls=[media_url],
                hashtags=hashtags,
                post_options=post_options
            )

        except Exception as e:
            logger.error(f"خطأ في نشر الفيديو: {e}")
            return {'status': 'error', 'message': f'خطأ في نشر الفيديو: {e}'}

    def upload_media(self, file_path: str) -> Dict[str, Any]:
        """رفع ملف وسائط"""
        try:
            if not self.client:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            # استخدام API رفع الملفات
            result = self.client.upload(file_path)

            if result.get('status') == 'success':
                logger.info(f"تم رفع الملف بنجاح: {file_path}")
                return result
            else:
                error_msg = result.get('message', 'خطأ في رفع الملف')
                logger.error(f"فشل رفع الملف: {error_msg}")
                return result

        except Exception as e:
            logger.error(f"خطأ في رفع الملف: {e}")
            return {'status': 'error', 'message': f'خطأ في رفع الملف: {e}'}

    def get_post_history(self, limit: int = 50) -> Dict[str, Any]:
        """الحصول على تاريخ المنشورات"""
        try:
            if not self.client:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            params = {'last': limit}
            if self.profile_key:
                params['profileKey'] = self.profile_key

            result = self.client.history(params)

            if result.get('status') == 'success':
                logger.info(f"تم الحصول على تاريخ المنشورات: {limit} منشور")
                return result
            else:
                error_msg = result.get('message', 'خطأ في الحصول على التاريخ')
                logger.error(f"فشل الحصول على التاريخ: {error_msg}")
                return result

        except Exception as e:
            logger.error(f"خطأ في الحصول على التاريخ: {e}")
            return {'status': 'error', 'message': f'خطأ في الحصول على التاريخ: {e}'}

    def delete_post(self, post_id: str) -> Dict[str, Any]:
        """حذف منشور"""
        try:
            if not self.client:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            delete_data = {'id': post_id}
            if self.profile_key:
                delete_data['profileKey'] = self.profile_key

            result = self.client.delete(delete_data)

            if result.get('status') == 'success':
                logger.info(f"تم حذف المنشور: {post_id}")
                return result
            else:
                error_msg = result.get('message', 'خطأ في حذف المنشور')
                logger.error(f"فشل حذف المنشور: {error_msg}")
                return result

        except Exception as e:
            logger.error(f"خطأ في حذف المنشور: {e}")
            return {'status': 'error', 'message': f'خطأ في حذف المنشور: {e}'}

    def get_analytics(self, post_id: str) -> Dict[str, Any]:
        """الحصول على إحصائيات منشور"""
        try:
            if not self.client:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            analytics_data = {'id': post_id}
            if self.profile_key:
                analytics_data['profileKey'] = self.profile_key

            result = self.client.analyticsPost(analytics_data)

            if result.get('status') == 'success':
                logger.info(f"تم الحصول على إحصائيات المنشور: {post_id}")
                return result
            else:
                error_msg = result.get('message', 'خطأ في الحصول على الإحصائيات')
                logger.error(f"فشل الحصول على الإحصائيات: {error_msg}")
                return result

        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات: {e}")
            return {'status': 'error', 'message': f'خطأ في الحصول على الإحصائيات: {e}'}

# إنشاء مثيل عام للعميل
ayrshare_client = AyrshareClient()
