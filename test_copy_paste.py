# -*- coding: utf-8 -*-
"""
اختبار وظائف النسخ واللصق في التطبيق
"""
import tkinter as tk
import customtkinter as ctk

def test_copy_paste():
    """اختبار النسخ واللصق"""
    print("🧪 اختبار وظائف النسخ واللصق")
    print("=" * 50)
    
    # إنشاء نافذة اختبار
    root = ctk.CTk()
    root.title("اختبار النسخ واللصق")
    root.geometry("600x400")
    
    # إنشاء مربع نص للاختبار
    text_frame = ctk.CTkFrame(root)
    text_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    ctk.CTkLabel(text_frame, text="اختبار النسخ واللصق", 
                font=("Arial", 16, "bold")).pack(pady=10)
    
    # مربع النص الرئيسي
    test_textbox = ctk.CTkTextbox(text_frame, height=150)
    test_textbox.pack(fill="x", padx=10, pady=5)
    
    # إدراج نص تجريبي
    test_content = """🎯 نص تجريبي للنسخ واللصق

هذا نص تجريبي يحتوي على:
- نصوص عربية وإنجليزية
- أرقام: 123456789
- رموز: !@#$%^&*()
- هاشتاجات: #اختبار #test #نسخ_ولصق

جرب الاختصارات التالية:
• Ctrl+C للنسخ
• Ctrl+V للصق
• Ctrl+X للقص
• Ctrl+A لتحديد الكل
• النقر الأيمن لقائمة السياق

✅ إذا كانت هذه الاختصارات تعمل، فالنسخ واللصق يعمل بنجاح!"""
    
    test_textbox.insert("1.0", test_content)
    
    # حقل الهاشتاجات للاختبار
    hashtags_frame = ctk.CTkFrame(text_frame)
    hashtags_frame.pack(fill="x", padx=10, pady=5)
    
    ctk.CTkLabel(hashtags_frame, text="اختبار الهاشتاجات:", width=150).pack(side="left", padx=5)
    hashtags_var = tk.StringVar(value="#اختبار #test #copy_paste")
    hashtags_entry = ctk.CTkEntry(hashtags_frame, textvariable=hashtags_var)
    hashtags_entry.pack(side="left", padx=5, fill="x", expand=True)
    
    # وظائف النسخ واللصق
    def copy_text(event=None):
        try:
            if test_textbox.selection_get():
                root.clipboard_clear()
                root.clipboard_append(test_textbox.selection_get())
                status_label.configure(text="✅ تم نسخ النص")
        except tk.TclError:
            pass
        return "break"
    
    def paste_text(event=None):
        try:
            clipboard_text = root.clipboard_get()
            if clipboard_text:
                test_textbox.insert(tk.INSERT, clipboard_text)
                status_label.configure(text="✅ تم لصق النص")
        except tk.TclError:
            pass
        return "break"
    
    def cut_text(event=None):
        try:
            if test_textbox.selection_get():
                root.clipboard_clear()
                root.clipboard_append(test_textbox.selection_get())
                test_textbox.delete(tk.SEL_FIRST, tk.SEL_LAST)
                status_label.configure(text="✅ تم قص النص")
        except tk.TclError:
            pass
        return "break"
    
    def select_all_text(event=None):
        test_textbox.tag_add(tk.SEL, "1.0", tk.END)
        test_textbox.mark_set(tk.INSERT, "1.0")
        test_textbox.see(tk.INSERT)
        status_label.configure(text="✅ تم تحديد جميع النص")
        return "break"
    
    def show_context_menu(event):
        try:
            context_menu = tk.Menu(root, tearoff=0)
            context_menu.add_command(label="نسخ (Ctrl+C)", command=copy_text)
            context_menu.add_command(label="لصق (Ctrl+V)", command=paste_text)
            context_menu.add_command(label="قص (Ctrl+X)", command=cut_text)
            context_menu.add_separator()
            context_menu.add_command(label="تحديد الكل (Ctrl+A)", command=select_all_text)
            context_menu.tk_popup(event.x_root, event.y_root)
        except Exception as e:
            print(f"خطأ في قائمة السياق: {e}")
    
    # ربط الاختصارات
    test_textbox.bind("<Control-c>", copy_text)
    test_textbox.bind("<Control-v>", paste_text)
    test_textbox.bind("<Control-a>", select_all_text)
    test_textbox.bind("<Control-x>", cut_text)
    test_textbox.bind("<Button-3>", show_context_menu)
    
    # شريط الحالة
    status_frame = ctk.CTkFrame(text_frame)
    status_frame.pack(fill="x", padx=10, pady=5)
    
    status_label = ctk.CTkLabel(status_frame, text="جاهز للاختبار - جرب الاختصارات!")
    status_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = ctk.CTkFrame(text_frame)
    buttons_frame.pack(fill="x", padx=10, pady=5)
    
    ctk.CTkButton(buttons_frame, text="اختبار النسخ", 
                 command=copy_text).pack(side="left", padx=5)
    ctk.CTkButton(buttons_frame, text="اختبار اللصق", 
                 command=paste_text).pack(side="left", padx=5)
    ctk.CTkButton(buttons_frame, text="تحديد الكل", 
                 command=select_all_text).pack(side="left", padx=5)
    
    def close_test():
        print("✅ اختبار النسخ واللصق مكتمل")
        root.destroy()
    
    ctk.CTkButton(buttons_frame, text="إغلاق الاختبار", 
                 command=close_test).pack(side="right", padx=5)
    
    print("🎯 تعليمات الاختبار:")
    print("1. حدد نص في المربع")
    print("2. اضغط Ctrl+C للنسخ")
    print("3. انقر في مكان آخر")
    print("4. اضغط Ctrl+V للصق")
    print("5. جرب النقر الأيمن لقائمة السياق")
    print("6. اختبر حقل الهاشتاجات أيضاً")
    print("\n✅ إذا عملت الاختصارات، فالنسخ واللصق يعمل!")
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    try:
        test_copy_paste()
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
