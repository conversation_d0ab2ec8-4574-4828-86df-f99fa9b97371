# -*- coding: utf-8 -*-
"""
ملف التشغيل الرئيسي المحسن
Ayrshare Social Media Manager
"""

import sys
import os
import subprocess
import time

def print_banner():
    """طباعة شعار البرنامج"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║           🚀 Ayrshare Social Media Manager 🚀                ║
    ║                                                              ║
    ║              برنامج إدارة وسائل التواصل الاجتماعي              ║
    ║                                                              ║
    ║    📱 Facebook  📸 Instagram  🎵 TikTok                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python():
    """فحص Python"""
    print("🔍 فحص Python...")

    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        print("   يرجى تحديث Python من: https://python.org")
        return False

    print(f"✅ Python {sys.version.split()[0]} - جاهز")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 فحص المتطلبات...")

    if not os.path.exists('requirements.txt'):
        print("❌ ملف requirements.txt غير موجود")
        return False

    try:
        # فحص إذا كانت المكتبات مثبتة
        import customtkinter
        import requests
        from PIL import Image
        # from ayrshare import SocialPost  # نستخدم عميل مخصوص
        print("✅ جميع المتطلبات مثبتة")
        return True

    except ImportError:
        print("⚠️ بعض المتطلبات مفقودة، جاري التثبيت...")

        try:
            # تثبيت المتطلبات
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ], capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                print("✅ تم تثبيت المتطلبات بنجاح")
                return True
            else:
                print(f"❌ فشل في التثبيت: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            print("❌ انتهت مهلة التثبيت")
            return False
        except Exception as e:
            print(f"❌ خطأ في التثبيت: {e}")
            return False

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص ملفات البرنامج...")

    required_files = [
        'main.py',
        'utils/config.py',
        'utils/logger.py',
        'database/database.py',
        'api/ayrshare_client.py',
        'gui/main_window.py'
    ]

    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} مفقود")
            missing_files.append(file_path)

    if missing_files:
        print(f"\n❌ ملفات مفقودة: {len(missing_files)}")
        return False

    print("✅ جميع الملفات موجودة")
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("\n📂 إنشاء المجلدات...")

    directories = ['logs', 'uploads', 'thumbnails', 'exports']

    for directory in directories:
        try:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"✅ تم إنشاء مجلد: {directory}")
            else:
                print(f"✅ مجلد موجود: {directory}")
        except Exception as e:
            print(f"⚠️ تحذير: فشل إنشاء مجلد {directory}: {e}")

def run_application():
    """تشغيل التطبيق"""
    print("\n🚀 بدء تشغيل التطبيق...")
    print("   (قد يستغرق بضع ثوانٍ لتحميل الواجهة الرسومية)")

    try:
        # إضافة المجلد الحالي لمسار Python
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

        # استيراد وتشغيل التطبيق
        from main import main

        print("✅ تم تحميل البرنامج")
        print("\n" + "="*50)
        print("🎉 مرحباً بك في Ayrshare Social Media Manager!")
        print("="*50)

        # تشغيل التطبيق
        main()

    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
    except ImportError as e:
        print(f"\n❌ خطأ في استيراد المكونات: {e}")
        print("💡 حاول تشغيل: pip install -r requirements.txt")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البرنامج: {e}")
        print("\n🔧 نصائح لحل المشكلة:")
        print("1. تأكد من تثبيت Python 3.8+")
        print("2. شغل: pip install -r requirements.txt")
        print("3. تأكد من وجود جميع ملفات البرنامج")
        print("4. راجع ملفات السجل في مجلد logs/")

def show_quick_help():
    """عرض مساعدة سريعة"""
    help_text = """
    📋 مساعدة سريعة:

    🔑 للبدء:
    1. احصل على API Key من: https://app.ayrshare.com
    2. اربط حساباتك على Facebook, Instagram, TikTok
    3. أدخل API Key في البرنامج
    4. ابدأ النشر!

    📚 للمزيد من المساعدة:
    - README.md - دليل شامل
    - QUICK_START.md - دليل البدء السريع
    - FAQ.md - أسئلة شائعة

    🆘 في حالة المشاكل:
    - شغل: python simple_test.py
    - راجع مجلد logs/ للأخطاء
    - تأكد من اتصال الإنترنت
    """
    print(help_text)

def main():
    """الدالة الرئيسية"""
    # طباعة الشعار
    print_banner()

    # فحص Python
    if not check_python():
        input("\nاضغط Enter للخروج...")
        return

    # تثبيت المتطلبات
    if not install_requirements():
        print("\n❌ فشل في تثبيت المتطلبات")
        print("💡 حاول تشغيل يدوياً: pip install -r requirements.txt")
        input("\nاضغط Enter للخروج...")
        return

    # فحص الملفات
    if not check_files():
        print("\n❌ ملفات البرنامج غير مكتملة")
        input("\nاضغط Enter للخروج...")
        return

    # إنشاء المجلدات
    create_directories()

    # عرض مساعدة سريعة
    show_quick_help()

    # تشغيل التطبيق
    run_application()

    print("\n👋 شكراً لاستخدام Ayrshare Social Media Manager!")

if __name__ == "__main__":
    main()
