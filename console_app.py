# -*- coding: utf-8 -*-
"""
نسخة وضع النص من Ayrshare Social Media Manager
"""
import os
import sys
import json
from datetime import datetime

def print_banner():
    """طباعة شعار التطبيق"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║           🚀 Ayrshare Social Media Manager 🚀                ║
    ║                                                              ║
    ║              برنامج إدارة وسائل التواصل الاجتماعي           ║
    ║                                                              ║
    ║    📱 Facebook  📸 Instagram  🎵 TikTok                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_system():
    """فحص النظام"""
    print("🔍 فحص النظام...")
    print(f"🐍 Python: {sys.version}")
    print(f"💻 النظام: {os.name}")
    print(f"📁 المجلد الحالي: {os.getcwd()}")
    print()

def check_files():
    """فحص الملفات المطلوبة"""
    print("📂 فحص الملفات...")
    
    required_files = [
        "main.py",
        "gui/main_window.py", 
        "gui/advanced_settings.py",
        "utils/config.py",
        "api/ayrshare_client.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - مفقود")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ ملفات مفقودة: {len(missing_files)}")
        return False
    else:
        print("\n✅ جميع الملفات موجودة")
        return True

def check_packages():
    """فحص المكتبات المطلوبة"""
    print("📦 فحص المكتبات...")
    
    required_packages = [
        "tkinter",
        "customtkinter", 
        "requests",
        "PIL"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == "PIL":
                import PIL
            elif package == "tkinter":
                import tkinter
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - غير مثبت")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ مكتبات مفقودة: {missing_packages}")
        print("💡 لتثبيتها: pip install " + " ".join(missing_packages))
        return False
    else:
        print("\n✅ جميع المكتبات مثبتة")
        return True

def main_menu():
    """القائمة الرئيسية"""
    while True:
        print("\n" + "="*60)
        print("📋 القائمة الرئيسية")
        print("="*60)
        print("1. 🚀 تشغيل الواجهة الرسومية")
        print("2. 🧪 اختبار الواجهة البسيطة") 
        print("3. 🔧 فحص النظام والملفات")
        print("4. 📦 تثبيت المتطلبات")
        print("5. ⚙️ إعدادات API")
        print("6. 📖 عرض الوثائق")
        print("0. ❌ خروج")
        print()
        
        choice = input("اختر رقماً (0-6): ").strip()
        
        if choice == "1":
            launch_gui()
        elif choice == "2":
            test_simple_gui()
        elif choice == "3":
            system_check()
        elif choice == "4":
            install_requirements()
        elif choice == "5":
            api_settings()
        elif choice == "6":
            show_docs()
        elif choice == "0":
            print("👋 شكراً لاستخدام Ayrshare Social Media Manager!")
            break
        else:
            print("❌ اختيار غير صحيح")

def launch_gui():
    """تشغيل الواجهة الرسومية"""
    print("\n🚀 تشغيل الواجهة الرسومية...")
    try:
        import subprocess
        result = subprocess.run([sys.executable, "main.py"], 
                              capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print("✅ تم تشغيل الواجهة الرسومية")
        else:
            print(f"❌ خطأ في تشغيل الواجهة: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("✅ الواجهة الرسومية تعمل في الخلفية")
    except Exception as e:
        print(f"❌ خطأ: {e}")

def test_simple_gui():
    """اختبار الواجهة البسيطة"""
    print("\n🧪 اختبار الواجهة البسيطة...")
    try:
        import subprocess
        subprocess.Popen([sys.executable, "simple_gui_test.py"])
        print("✅ تم تشغيل اختبار الواجهة")
    except Exception as e:
        print(f"❌ خطأ: {e}")

def system_check():
    """فحص شامل للنظام"""
    print("\n🔍 فحص شامل للنظام...")
    check_system()
    files_ok = check_files()
    packages_ok = check_packages()
    
    if files_ok and packages_ok:
        print("\n🎉 النظام جاهز للتشغيل!")
    else:
        print("\n⚠️ يحتاج النظام إلى إصلاحات")

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المتطلبات...")
    try:
        import subprocess
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت المتطلبات بنجاح")
        else:
            print(f"❌ خطأ في التثبيت: {result.stderr}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

def api_settings():
    """إعدادات API"""
    print("\n⚙️ إعدادات API")
    print("🔑 للحصول على API Key:")
    print("1. اذهب إلى: https://app.ayrshare.com")
    print("2. أنشئ حساب أو سجل دخول")
    print("3. اذهب إلى قسم API")
    print("4. انسخ API Key")
    print()
    
    api_key = input("أدخل API Key (أو اضغط Enter للتخطي): ").strip()
    
    if api_key:
        try:
            # حفظ في ملف الإعدادات
            config = {"api_key": api_key}
            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print("✅ تم حفظ API Key")
        except Exception as e:
            print(f"❌ خطأ في الحفظ: {e}")

def show_docs():
    """عرض الوثائق"""
    print("\n📖 الوثائق المتاحة:")
    
    docs = [
        ("README.md", "دليل شامل"),
        ("QUICK_START.md", "دليل البدء السريع"),
        ("FAQ.md", "أسئلة شائعة"),
        ("FEATURES.md", "قائمة الميزات")
    ]
    
    for doc_file, description in docs:
        if os.path.exists(doc_file):
            print(f"✅ {doc_file} - {description}")
        else:
            print(f"❌ {doc_file} - غير موجود")

if __name__ == "__main__":
    os.system('cls' if os.name == 'nt' else 'clear')  # مسح الشاشة
    print_banner()
    check_system()
    main_menu()
