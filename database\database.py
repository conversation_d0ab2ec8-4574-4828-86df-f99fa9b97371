# -*- coding: utf-8 -*-
"""
إدارة قاعدة البيانات
"""
import sqlite3
import os
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from .models import (
    Post, ScheduledPost, RateLimit, ApiUsage, MediaFile,
    DatabaseHelper
)
from utils.logger import logger

class Database:
    """مدير قاعدة البيانات"""

    def __init__(self, db_path: str = "ayrshare_app.db"):
        self.db_path = db_path
        self.init_database()

    def get_connection(self) -> sqlite3.Connection:
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = DatabaseHelper.dict_factory
        return conn

    def init_database(self) -> None:
        """إنشاء جداول قاعدة البيانات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # جدول المنشورات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS posts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        title TEXT NOT NULL,
                        content TEXT NOT NULL,
                        media_paths TEXT,
                        platforms TEXT,
                        hashtags TEXT,
                        scheduled_time TEXT,
                        status TEXT DEFAULT 'draft',
                        created_at TEXT,
                        published_at TEXT,
                        ayrshare_id TEXT,
                        error_message TEXT
                    )
                ''')

                # جدول المنشورات المجدولة
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS scheduled_posts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        post_id INTEGER,
                        scheduled_time TEXT NOT NULL,
                        repeat_type TEXT DEFAULT 'none',
                        repeat_interval INTEGER DEFAULT 1,
                        repeat_end_date TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TEXT,
                        FOREIGN KEY (post_id) REFERENCES posts (id)
                    )
                ''')

                # جدول حدود المعدل
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS rate_limits (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        platform TEXT NOT NULL,
                        limit_type TEXT NOT NULL,
                        current_count INTEGER DEFAULT 0,
                        max_count INTEGER NOT NULL,
                        reset_time TEXT NOT NULL,
                        created_at TEXT
                    )
                ''')

                # جدول استخدام API
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS api_usage (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        endpoint TEXT NOT NULL,
                        method TEXT NOT NULL,
                        status_code INTEGER,
                        response_time REAL,
                        request_data TEXT,
                        response_data TEXT,
                        created_at TEXT
                    )
                ''')

                # جدول ملفات الوسائط
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS media_files (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        filename TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        file_type TEXT NOT NULL,
                        file_size INTEGER,
                        duration REAL,
                        dimensions TEXT,
                        thumbnail_path TEXT,
                        uploaded_at TEXT
                    )
                ''')

                conn.commit()
                logger.info("تم إنشاء قاعدة البيانات بنجاح")

        except Exception as e:
            logger.error(f"خطأ في إنشاء قاعدة البيانات: {e}")

    # ===== عمليات المنشورات =====

    def create_post(self, post: Post) -> Optional[int]:
        """إنشاء منشور جديد"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                post_data = DatabaseHelper.post_to_dict(post)
                post_data.pop('id', None)  # إزالة المعرف للإدراج

                columns = ', '.join(post_data.keys())
                placeholders = ', '.join(['?' for _ in post_data])

                cursor.execute(
                    f"INSERT INTO posts ({columns}) VALUES ({placeholders})",
                    list(post_data.values())
                )

                post_id = cursor.lastrowid
                conn.commit()
                logger.info(f"تم إنشاء منشور جديد بالمعرف: {post_id}")
                return post_id

        except Exception as e:
            logger.error(f"خطأ في إنشاء المنشور: {e}")
            return None

    def get_post(self, post_id: int) -> Optional[Post]:
        """الحصول على منشور بالمعرف"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM posts WHERE id = ?", (post_id,))
                row = cursor.fetchone()

                if row:
                    return DatabaseHelper.dict_to_post(row)
                return None

        except Exception as e:
            logger.error(f"خطأ في الحصول على المنشور: {e}")
            return None

    def get_posts(self, status: Optional[str] = None, limit: int = 100) -> List[Post]:
        """الحصول على قائمة المنشورات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if status:
                    cursor.execute(
                        "SELECT * FROM posts WHERE status = ? ORDER BY created_at DESC LIMIT ?",
                        (status, limit)
                    )
                else:
                    cursor.execute(
                        "SELECT * FROM posts ORDER BY created_at DESC LIMIT ?",
                        (limit,)
                    )

                rows = cursor.fetchall()
                return [DatabaseHelper.dict_to_post(row) for row in rows]

        except Exception as e:
            logger.error(f"خطأ في الحصول على المنشورات: {e}")
            return []

    def update_post(self, post: Post) -> bool:
        """تحديث منشور"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                post_data = DatabaseHelper.post_to_dict(post)
                post_id = post_data.pop('id')

                set_clause = ', '.join([f"{key} = ?" for key in post_data.keys()])

                cursor.execute(
                    f"UPDATE posts SET {set_clause} WHERE id = ?",
                    list(post_data.values()) + [post_id]
                )

                conn.commit()
                logger.info(f"تم تحديث المنشور: {post_id}")
                return True

        except Exception as e:
            logger.error(f"خطأ في تحديث المنشور: {e}")
            return False

    def delete_post(self, post_id: int) -> bool:
        """حذف منشور"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM posts WHERE id = ?", (post_id,))
                conn.commit()
                logger.info(f"تم حذف المنشور: {post_id}")
                return True

        except Exception as e:
            logger.error(f"خطأ في حذف المنشور: {e}")
            return False

    # ===== عمليات الجدولة =====

    def create_scheduled_post(self, scheduled_post: ScheduledPost) -> Optional[int]:
        """إنشاء منشور مجدول"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                data = DatabaseHelper.scheduled_post_to_dict(scheduled_post)
                data.pop('id', None)

                columns = ', '.join(data.keys())
                placeholders = ', '.join(['?' for _ in data])

                cursor.execute(
                    f"INSERT INTO scheduled_posts ({columns}) VALUES ({placeholders})",
                    list(data.values())
                )

                schedule_id = cursor.lastrowid
                conn.commit()
                logger.info(f"تم إنشاء جدولة جديدة بالمعرف: {schedule_id}")
                return schedule_id

        except Exception as e:
            logger.error(f"خطأ في إنشاء الجدولة: {e}")
            return None

    def get_scheduled_posts(self, active_only: bool = True) -> List[ScheduledPost]:
        """الحصول على المنشورات المجدولة"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if active_only:
                    cursor.execute(
                        "SELECT * FROM scheduled_posts WHERE is_active = 1 ORDER BY scheduled_time"
                    )
                else:
                    cursor.execute(
                        "SELECT * FROM scheduled_posts ORDER BY scheduled_time"
                    )

                rows = cursor.fetchall()
                return [DatabaseHelper.dict_to_scheduled_post(row) for row in rows]

        except Exception as e:
            logger.error(f"خطأ في الحصول على المنشورات المجدولة: {e}")
            return []

    def get_due_posts(self) -> List[ScheduledPost]:
        """الحصول على المنشورات المستحقة للنشر"""
        try:
            current_time = datetime.now().isoformat()
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """SELECT * FROM scheduled_posts
                       WHERE is_active = 1 AND scheduled_time <= ?
                       ORDER BY scheduled_time""",
                    (current_time,)
                )

                rows = cursor.fetchall()
                return [DatabaseHelper.dict_to_scheduled_post(row) for row in rows]

        except Exception as e:
            logger.error(f"خطأ في الحصول على المنشورات المستحقة: {e}")
            return []

    def update_scheduled_post(self, scheduled_post: ScheduledPost) -> bool:
        """تحديث منشور مجدول"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                data = DatabaseHelper.scheduled_post_to_dict(scheduled_post)
                schedule_id = data.pop('id')

                set_clause = ', '.join([f"{key} = ?" for key in data.keys()])

                cursor.execute(
                    f"UPDATE scheduled_posts SET {set_clause} WHERE id = ?",
                    list(data.values()) + [schedule_id]
                )

                conn.commit()
                logger.info(f"تم تحديث الجدولة: {schedule_id}")
                return True

        except Exception as e:
            logger.error(f"خطأ في تحديث الجدولة: {e}")
            return False

# إنشاء مثيل عام لقاعدة البيانات
db = Database()
