# -*- coding: utf-8 -*-
"""
اختبار بسيط للواجهة الرسومية
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

def test_basic_gui():
    """اختبار الواجهة الأساسية"""
    print("🔍 بدء اختبار الواجهة الرسومية...")
    
    try:
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.title("🚀 Ayrshare Social Media Manager - اختبار")
        root.geometry("800x600")
        root.configure(bg='#2b2b2b')
        
        # جعل النافذة في المقدمة
        root.lift()
        root.attributes('-topmost', True)
        root.after_idle(root.attributes, '-topmost', False)
        
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # إنشاء إطار رئيسي
        main_frame = tk.Frame(root, bg='#2b2b2b', padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)
        
        # عنوان التطبيق
        title_label = tk.Label(
            main_frame, 
            text="🚀 Ayrshare Social Media Manager",
            font=("Arial", 20, "bold"),
            fg='white',
            bg='#2b2b2b'
        )
        title_label.pack(pady=20)
        
        # رسالة ترحيب
        welcome_label = tk.Label(
            main_frame,
            text="مرحباً! هذا اختبار للواجهة الرسومية",
            font=("Arial", 14),
            fg='lightblue',
            bg='#2b2b2b'
        )
        welcome_label.pack(pady=10)
        
        # معلومات النظام
        info_text = f"""
📱 المنصات المدعومة: Facebook, Instagram, TikTok
🐍 Python: {sys.version.split()[0]}
💻 النظام: {os.name}
📁 المجلد: {os.getcwd()}
        """
        
        info_label = tk.Label(
            main_frame,
            text=info_text,
            font=("Arial", 10),
            fg='lightgreen',
            bg='#2b2b2b',
            justify='left'
        )
        info_label.pack(pady=20)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg='#2b2b2b')
        buttons_frame.pack(pady=20)
        
        # زر اختبار
        def show_success():
            messagebox.showinfo("نجح!", "الواجهة الرسومية تعمل بنجاح! ✅")
        
        test_button = tk.Button(
            buttons_frame,
            text="🧪 اختبار الواجهة",
            command=show_success,
            font=("Arial", 12, "bold"),
            bg='#4CAF50',
            fg='white',
            padx=20,
            pady=10
        )
        test_button.pack(side="left", padx=10)
        
        # زر تشغيل التطبيق الكامل
        def run_full_app():
            try:
                root.destroy()
                import subprocess
                subprocess.Popen([sys.executable, "main.py"])
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تشغيل التطبيق الكامل: {e}")
        
        full_app_button = tk.Button(
            buttons_frame,
            text="🚀 تشغيل التطبيق الكامل",
            command=run_full_app,
            font=("Arial", 12, "bold"),
            bg='#2196F3',
            fg='white',
            padx=20,
            pady=10
        )
        full_app_button.pack(side="left", padx=10)
        
        # زر إغلاق
        def close_app():
            root.quit()
            root.destroy()
        
        close_button = tk.Button(
            buttons_frame,
            text="❌ إغلاق",
            command=close_app,
            font=("Arial", 12, "bold"),
            bg='#f44336',
            fg='white',
            padx=20,
            pady=10
        )
        close_button.pack(side="left", padx=10)
        
        # شريط الحالة
        status_frame = tk.Frame(root, bg='#1e1e1e', height=30)
        status_frame.pack(fill="x", side="bottom")
        
        status_label = tk.Label(
            status_frame,
            text="✅ الواجهة الرسومية تعمل بنجاح",
            font=("Arial", 10),
            fg='lightgreen',
            bg='#1e1e1e'
        )
        status_label.pack(side="left", padx=10, pady=5)
        
        print("✅ تم إعداد جميع عناصر الواجهة")
        print("🖥️ عرض النافذة...")
        
        # تمركز النافذة
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (800 // 2)
        y = (root.winfo_screenheight() // 2) - (600 // 2)
        root.geometry(f"800x600+{x}+{y}")
        
        # عرض النافذة
        root.deiconify()
        root.focus_force()
        
        print("🎯 النافذة معروضة الآن!")
        print("📍 ابحث عن نافذة بعنوان: 'Ayrshare Social Media Manager - اختبار'")
        
        # تشغيل حلقة الأحداث
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 اختبار الواجهة الرسومية لـ Ayrshare Social Media Manager")
    print("=" * 60)
    
    success = test_basic_gui()
    
    if success:
        print("✅ تم اختبار الواجهة بنجاح!")
    else:
        print("❌ فشل في اختبار الواجهة")
        print("💡 تأكد من أن نظامك يدعم الواجهات الرسومية")
