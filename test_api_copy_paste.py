# -*- coding: utf-8 -*-
"""
اختبار النسخ واللصق بعد تغيير API Key
"""
import tkinter as tk
import customtkinter as ctk
import time

def test_api_copy_paste():
    """اختبار النسخ واللصق بعد تغيير API Key"""
    print("🧪 اختبار النسخ واللصق بعد تغيير API Key")
    print("=" * 60)
    
    # إنشاء نافذة اختبار
    root = ctk.CTk()
    root.title("اختبار API Key والنسخ واللصق")
    root.geometry("800x600")
    
    # إنشاء الإطار الرئيسي
    main_frame = ctk.CTkFrame(root)
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    ctk.CTkLabel(main_frame, text="اختبار API Key والنسخ واللصق", 
                font=("Arial", 18, "bold")).pack(pady=10)
    
    # إطار API Key
    api_frame = ctk.CTkFrame(main_frame)
    api_frame.pack(fill="x", padx=10, pady=10)
    
    ctk.CTkLabel(api_frame, text="محاكاة تغيير API Key", 
                font=("Arial", 14, "bold")).pack(pady=5)
    
    # حقل API Key
    api_key_frame = ctk.CTkFrame(api_frame)
    api_key_frame.pack(fill="x", padx=10, pady=5)
    
    ctk.CTkLabel(api_key_frame, text="API Key:", width=100).pack(side="left", padx=5)
    api_key_var = tk.StringVar(value="test_api_key_12345")
    api_key_entry = ctk.CTkEntry(api_key_frame, textvariable=api_key_var, width=300)
    api_key_entry.pack(side="left", padx=5, fill="x", expand=True)
    
    # إطار النص للاختبار
    text_frame = ctk.CTkFrame(main_frame)
    text_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    ctk.CTkLabel(text_frame, text="مربع النص للاختبار", 
                font=("Arial", 14, "bold")).pack(pady=5)
    
    # مربع النص الرئيسي
    test_textbox = ctk.CTkTextbox(text_frame, height=200)
    test_textbox.pack(fill="x", padx=10, pady=5)
    
    # إدراج نص تجريبي
    test_content = """🎯 نص تجريبي لاختبار النسخ واللصق بعد تغيير API Key

هذا النص يجب أن يدعم النسخ واللصق حتى بعد:
- تغيير API Key
- اختبار الاتصال
- حفظ الإعدادات
- إعادة تحميل الإعدادات

جرب الاختصارات:
• Ctrl+C للنسخ
• Ctrl+V للصق
• Ctrl+X للقص
• Ctrl+A لتحديد الكل
• النقر الأيمن لقائمة السياق

✅ إذا كانت هذه الاختصارات تعمل بعد تغيير API Key، فالمشكلة محلولة!"""
    
    test_textbox.insert("1.0", test_content)
    
    # حقل الهاشتاجات للاختبار
    hashtags_frame = ctk.CTkFrame(text_frame)
    hashtags_frame.pack(fill="x", padx=10, pady=5)
    
    ctk.CTkLabel(hashtags_frame, text="الهاشتاجات:", width=100).pack(side="left", padx=5)
    hashtags_var = tk.StringVar(value="#اختبار #api_key #copy_paste")
    hashtags_entry = ctk.CTkEntry(hashtags_frame, textvariable=hashtags_var)
    hashtags_entry.pack(side="left", padx=5, fill="x", expand=True)
    
    # شريط الحالة
    status_frame = ctk.CTkFrame(main_frame)
    status_frame.pack(fill="x", padx=10, pady=5)
    
    status_label = ctk.CTkLabel(status_frame, text="جاهز للاختبار")
    status_label.pack(pady=5)
    
    # متغيرات للتتبع
    copy_paste_working = {"before": False, "after": False}
    
    # وظائف النسخ واللصق
    def copy_text(event=None):
        try:
            selected_text = test_textbox.get("sel.first", "sel.last")
            if selected_text:
                root.clipboard_clear()
                root.clipboard_append(selected_text)
                status_label.configure(text="✅ تم نسخ النص المحدد")
                copy_paste_working["after"] = True
                return "break"
        except tk.TclError:
            try:
                all_text = test_textbox.get("1.0", "end-1c")
                if all_text.strip():
                    root.clipboard_clear()
                    root.clipboard_append(all_text)
                    status_label.configure(text="✅ تم نسخ كل النص")
                    copy_paste_working["after"] = True
            except:
                pass
        return "break"
    
    def paste_text(event=None):
        try:
            clipboard_text = root.clipboard_get()
            if clipboard_text:
                cursor_pos = test_textbox.index(tk.INSERT)
                test_textbox.insert(cursor_pos, clipboard_text)
                status_label.configure(text="✅ تم لصق النص")
                copy_paste_working["after"] = True
        except tk.TclError:
            pass
        return "break"
    
    def cut_text(event=None):
        try:
            selected_text = test_textbox.get("sel.first", "sel.last")
            if selected_text:
                root.clipboard_clear()
                root.clipboard_append(selected_text)
                test_textbox.delete("sel.first", "sel.last")
                status_label.configure(text="✅ تم قص النص")
                copy_paste_working["after"] = True
        except tk.TclError:
            pass
        return "break"
    
    def select_all_text(event=None):
        test_textbox.tag_add("sel", "1.0", "end-1c")
        test_textbox.mark_set(tk.INSERT, "1.0")
        test_textbox.see(tk.INSERT)
        status_label.configure(text="✅ تم تحديد جميع النص")
        return "break"
    
    def show_context_menu(event):
        try:
            context_menu = tk.Menu(root, tearoff=0)
            context_menu.add_command(label="نسخ (Ctrl+C)", command=copy_text)
            context_menu.add_command(label="لصق (Ctrl+V)", command=paste_text)
            context_menu.add_command(label="قص (Ctrl+X)", command=cut_text)
            context_menu.add_separator()
            context_menu.add_command(label="تحديد الكل (Ctrl+A)", command=select_all_text)
            context_menu.tk_popup(event.x_root, event.y_root)
        except Exception as e:
            print(f"خطأ في قائمة السياق: {e}")
    
    # وظائف النسخ واللصق للهاشتاجات
    def copy_hashtags(event=None):
        try:
            try:
                selected_text = hashtags_entry.selection_get()
                if selected_text:
                    root.clipboard_clear()
                    root.clipboard_append(selected_text)
                    status_label.configure(text="✅ تم نسخ الهاشتاجات المحددة")
                    return "break"
            except:
                all_text = hashtags_entry.get()
                if all_text:
                    root.clipboard_clear()
                    root.clipboard_append(all_text)
                    status_label.configure(text="✅ تم نسخ كل الهاشتاجات")
        except:
            pass
        return "break"
    
    def paste_hashtags(event=None):
        try:
            clipboard_text = root.clipboard_get()
            if clipboard_text:
                cursor_pos = hashtags_entry.index(tk.INSERT)
                hashtags_entry.insert(cursor_pos, clipboard_text)
                status_label.configure(text="✅ تم لصق الهاشتاجات")
        except tk.TclError:
            pass
        return "break"
    
    # ربط الاختصارات الأولي
    def bind_shortcuts():
        test_textbox.bind("<Control-c>", copy_text)
        test_textbox.bind("<Control-v>", paste_text)
        test_textbox.bind("<Control-a>", select_all_text)
        test_textbox.bind("<Control-x>", cut_text)
        test_textbox.bind("<Button-3>", show_context_menu)
        
        hashtags_entry.bind("<Control-c>", copy_hashtags)
        hashtags_entry.bind("<Control-v>", paste_hashtags)
        
        status_label.configure(text="✅ تم ربط اختصارات النسخ واللصق")
    
    # محاكاة تغيير API Key
    def simulate_api_change():
        status_label.configure(text="🔄 محاكاة تغيير API Key...")
        root.update()
        time.sleep(1)
        
        # تغيير API Key
        new_api_key = f"new_api_key_{int(time.time())}"
        api_key_var.set(new_api_key)
        
        status_label.configure(text="🔄 محاكاة اختبار الاتصال...")
        root.update()
        time.sleep(1)
        
        # إعادة ربط الاختصارات (هذا ما يجب أن يحدث في التطبيق الحقيقي)
        bind_shortcuts()
        
        status_label.configure(text="✅ تم تغيير API Key وإعادة ربط الاختصارات")
    
    # اختبار النسخ واللصق قبل وبعد
    def test_copy_paste_before():
        copy_paste_working["before"] = True
        status_label.configure(text="✅ النسخ واللصق يعمل قبل تغيير API Key")
    
    def test_copy_paste_after():
        if copy_paste_working["after"]:
            status_label.configure(text="🎉 النسخ واللصق يعمل بعد تغيير API Key!")
        else:
            status_label.configure(text="❌ النسخ واللصق لا يعمل بعد تغيير API Key")
    
    # أزرار الاختبار
    buttons_frame = ctk.CTkFrame(main_frame)
    buttons_frame.pack(fill="x", padx=10, pady=10)
    
    ctk.CTkButton(buttons_frame, text="1. ربط الاختصارات", 
                 command=bind_shortcuts).pack(side="left", padx=5)
    ctk.CTkButton(buttons_frame, text="2. اختبار النسخ واللصق", 
                 command=test_copy_paste_before).pack(side="left", padx=5)
    ctk.CTkButton(buttons_frame, text="3. محاكاة تغيير API Key", 
                 command=simulate_api_change).pack(side="left", padx=5)
    ctk.CTkButton(buttons_frame, text="4. اختبار بعد التغيير", 
                 command=test_copy_paste_after).pack(side="left", padx=5)
    
    def close_test():
        print("✅ اختبار API Key والنسخ واللصق مكتمل")
        if copy_paste_working["before"] and copy_paste_working["after"]:
            print("🎉 النسخ واللصق يعمل قبل وبعد تغيير API Key!")
        else:
            print("❌ هناك مشكلة في النسخ واللصق")
        root.destroy()
    
    ctk.CTkButton(buttons_frame, text="إغلاق الاختبار", 
                 command=close_test).pack(side="right", padx=5)
    
    print("🎯 تعليمات الاختبار:")
    print("1. اضغط 'ربط الاختصارات' أولاً")
    print("2. جرب النسخ واللصق واضغط 'اختبار النسخ واللصق'")
    print("3. اضغط 'محاكاة تغيير API Key'")
    print("4. جرب النسخ واللصق مرة أخرى واضغط 'اختبار بعد التغيير'")
    print("5. إذا عمل النسخ واللصق في الخطوتين، فالمشكلة محلولة!")
    
    # ربط الاختصارات في البداية
    bind_shortcuts()
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    try:
        test_api_copy_paste()
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
