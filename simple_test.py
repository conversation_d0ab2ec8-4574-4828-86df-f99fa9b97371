# -*- coding: utf-8 -*-
"""
اختبار بسيط للبرنامج
"""

print("🚀 اختبار Ayrshare Social Media Manager")
print("=" * 50)

# اختبار استيراد Python الأساسي
try:
    import sys
    import os
    print(f"✅ Python {sys.version}")
except Exception as e:
    print(f"❌ خطأ في Python: {e}")
    exit(1)

# اختبار المكتبات الأساسية
try:
    import tkinter as tk
    print("✅ tkinter متوفر")
except ImportError:
    print("❌ tkinter غير متوفر")

try:
    import sqlite3
    print("✅ sqlite3 متوفر")
except ImportError:
    print("❌ sqlite3 غير متوفر")

try:
    import json
    print("✅ json متوفر")
except ImportError:
    print("❌ json غير متوفر")

# اختبار المكتبات الخارجية
try:
    import requests
    print("✅ requests متوفر")
except ImportError:
    print("❌ requests غير متوفر - شغل: pip install requests")

try:
    import customtkinter
    print("✅ customtkinter متوفر")
except ImportError:
    print("❌ customtkinter غير متوفر - شغل: pip install customtkinter")

try:
    from PIL import Image
    print("✅ Pillow متوفر")
except ImportError:
    print("❌ Pillow غير متوفر - شغل: pip install Pillow")

try:
    from ayrshare import SocialPost
    print("✅ social-post-api متوفر")
except ImportError:
    print("❌ social-post-api غير متوفر - شغل: pip install social-post-api")

# اختبار الملفات
files_to_check = [
    'main.py',
    'requirements.txt',
    'utils/config.py',
    'utils/logger.py',
    'database/database.py',
    'database/models.py',
    'api/ayrshare_client.py',
    'api/rate_limiter.py',
    'gui/main_window.py'
]

print("\n📁 فحص الملفات:")
for file_path in files_to_check:
    if os.path.exists(file_path):
        print(f"✅ {file_path}")
    else:
        print(f"❌ {file_path} مفقود")

print("\n🎉 انتهى الاختبار البسيط!")
print("💡 إذا كانت جميع العلامات خضراء، يمكنك تشغيل: python main.py")
