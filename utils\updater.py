# -*- coding: utf-8 -*-
"""
نظام التحديثات التلقائي
"""
import requests
import json
import os
import subprocess
import threading
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple
from utils.logger import logger
from utils.config import config

class UpdateManager:
    """مدير التحديثات"""
    
    def __init__(self):
        self.current_version = "1.0.0"
        self.update_url = "https://api.github.com/repos/your-repo/ayrshare-manager/releases/latest"
        self.check_interval = config.get('updates.check_interval_hours', 24)
        self.auto_check = config.get('updates.auto_check', True)
        self.last_check = config.get('updates.last_check', None)
        
    def check_for_updates(self, show_no_updates: bool = False) -> Tuple[bool, Optional[Dict]]:
        """فحص التحديثات المتاحة"""
        try:
            logger.info("فحص التحديثات المتاحة...")
            
            # محاكاة فحص التحديثات (يمكن تخصيصها لاحقاً)
            # في التطبيق الحقيقي، ستتصل بـ GitHub API أو خادم التحديثات
            
            # تحديث وقت آخر فحص
            config.set('updates.last_check', datetime.now().isoformat())
            
            # محاكاة عدم وجود تحديثات
            if show_no_updates:
                logger.info("لا توجد تحديثات متاحة")
                return False, None
            
            # محاكاة وجود تحديث (للاختبار)
            fake_update = {
                'version': '1.1.0',
                'release_date': '2024-02-01',
                'download_url': 'https://github.com/your-repo/releases/download/v1.1.0/update.zip',
                'changelog': [
                    'إضافة ميزة تكرار المنشورات',
                    'تحسين واجهة الجدولة',
                    'إصلاح أخطاء النشر',
                    'تحسين الأداء'
                ],
                'size_mb': 15.2,
                'required': False
            }
            
            if self._is_newer_version(fake_update['version']):
                logger.info(f"تحديث متاح: الإصدار {fake_update['version']}")
                return True, fake_update
            
            return False, None
            
        except Exception as e:
            logger.error(f"خطأ في فحص التحديثات: {e}")
            return False, None
    
    def _is_newer_version(self, new_version: str) -> bool:
        """فحص إذا كان الإصدار الجديد أحدث"""
        try:
            current_parts = [int(x) for x in self.current_version.split('.')]
            new_parts = [int(x) for x in new_version.split('.')]
            
            # مقارنة أجزاء الإصدار
            for i in range(max(len(current_parts), len(new_parts))):
                current_part = current_parts[i] if i < len(current_parts) else 0
                new_part = new_parts[i] if i < len(new_parts) else 0
                
                if new_part > current_part:
                    return True
                elif new_part < current_part:
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في مقارنة الإصدارات: {e}")
            return False
    
    def should_check_for_updates(self) -> bool:
        """فحص إذا كان يجب فحص التحديثات"""
        if not self.auto_check:
            return False
        
        if not self.last_check:
            return True
        
        try:
            last_check_time = datetime.fromisoformat(self.last_check)
            time_since_check = datetime.now() - last_check_time
            
            return time_since_check.total_seconds() >= (self.check_interval * 3600)
            
        except Exception as e:
            logger.error(f"خطأ في فحص وقت التحديث: {e}")
            return True
    
    def download_update(self, update_info: Dict, progress_callback=None) -> bool:
        """تحميل التحديث"""
        try:
            download_url = update_info.get('download_url')
            if not download_url:
                logger.error("رابط التحميل غير متوفر")
                return False
            
            # إنشاء مجلد التحديثات
            updates_dir = "updates"
            if not os.path.exists(updates_dir):
                os.makedirs(updates_dir)
            
            # اسم ملف التحديث
            update_filename = f"update_v{update_info['version']}.zip"
            update_path = os.path.join(updates_dir, update_filename)
            
            logger.info(f"بدء تحميل التحديث: {update_info['version']}")
            
            # محاكاة التحميل (في التطبيق الحقيقي، ستحمل الملف فعلياً)
            if progress_callback:
                for progress in range(0, 101, 10):
                    progress_callback(progress)
                    import time
                    time.sleep(0.1)  # محاكاة وقت التحميل
            
            # محاكاة حفظ الملف
            with open(update_path, 'w') as f:
                f.write(f"Update file for version {update_info['version']}")
            
            logger.info(f"تم تحميل التحديث: {update_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحميل التحديث: {e}")
            return False
    
    def install_update(self, update_info: Dict) -> bool:
        """تثبيت التحديث"""
        try:
            logger.info(f"بدء تثبيت التحديث: {update_info['version']}")
            
            # في التطبيق الحقيقي، ستقوم بـ:
            # 1. استخراج ملف التحديث
            # 2. نسخ الملفات الجديدة
            # 3. تحديث قاعدة البيانات إذا لزم الأمر
            # 4. إعادة تشغيل التطبيق
            
            # محاكاة التثبيت
            logger.info("استخراج ملفات التحديث...")
            logger.info("نسخ الملفات الجديدة...")
            logger.info("تحديث الإعدادات...")
            
            # تحديث رقم الإصدار
            self.current_version = update_info['version']
            config.set('app.version', self.current_version)
            
            logger.info(f"تم تثبيت التحديث بنجاح: {update_info['version']}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تثبيت التحديث: {e}")
            return False
    
    def create_backup(self) -> bool:
        """إنشاء نسخة احتياطية قبل التحديث"""
        try:
            backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            if not os.path.exists('backups'):
                os.makedirs('backups')
            
            backup_path = os.path.join('backups', backup_dir)
            os.makedirs(backup_path)
            
            # نسخ الملفات المهمة
            important_files = [
                'config.json',
                'ayrshare_app.db',
                'main.py'
            ]
            
            for file_name in important_files:
                if os.path.exists(file_name):
                    import shutil
                    shutil.copy2(file_name, backup_path)
            
            logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def rollback_update(self, backup_path: str) -> bool:
        """التراجع عن التحديث"""
        try:
            logger.info(f"التراجع عن التحديث من: {backup_path}")
            
            # استعادة الملفات من النسخة الاحتياطية
            import shutil
            
            backup_files = os.listdir(backup_path)
            for file_name in backup_files:
                backup_file = os.path.join(backup_path, file_name)
                if os.path.isfile(backup_file):
                    shutil.copy2(backup_file, file_name)
            
            logger.info("تم التراجع عن التحديث بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التراجع عن التحديث: {e}")
            return False
    
    def get_update_history(self) -> list:
        """الحصول على تاريخ التحديثات"""
        try:
            history = config.get('updates.history', [])
            return history
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على تاريخ التحديثات: {e}")
            return []
    
    def add_to_update_history(self, update_info: Dict):
        """إضافة تحديث لتاريخ التحديثات"""
        try:
            history = self.get_update_history()
            
            history_entry = {
                'version': update_info['version'],
                'installed_date': datetime.now().isoformat(),
                'changelog': update_info.get('changelog', [])
            }
            
            history.append(history_entry)
            
            # الاحتفاظ بآخر 10 تحديثات فقط
            if len(history) > 10:
                history = history[-10:]
            
            config.set('updates.history', history)
            
        except Exception as e:
            logger.error(f"خطأ في إضافة التحديث للتاريخ: {e}")
    
    def enable_auto_updates(self, enabled: bool = True):
        """تفعيل أو إلغاء التحديثات التلقائية"""
        self.auto_check = enabled
        config.set('updates.auto_check', enabled)
        logger.info(f"التحديثات التلقائية: {'مفعلة' if enabled else 'معطلة'}")
    
    def set_check_interval(self, hours: int):
        """تعيين فترة فحص التحديثات"""
        self.check_interval = hours
        config.set('updates.check_interval_hours', hours)
        logger.info(f"تم تعيين فترة فحص التحديثات: {hours} ساعة")

# إنشاء مثيل عام لمدير التحديثات
update_manager = UpdateManager()
