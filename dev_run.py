# -*- coding: utf-8 -*-
"""
تشغيل البرنامج للمطورين مع تشخيص مفصل
"""

import sys
import os
import traceback

def check_python_version():
    """فحص إصدار Python"""
    print(f"🐍 Python {sys.version}")
    if sys.version_info < (3, 10):
        print("⚠️ تحذير: يُنصح بـ Python 3.10 أو أحدث")
    return True

def check_imports():
    """فحص الاستيرادات"""
    print("\n📦 فحص المكتبات:")

    # المكتبات الأساسية
    basic_modules = {
        'tkinter': 'مدمج مع Python',
        'sqlite3': 'مدمج مع Python',
        'json': 'مدمج مع Python',
        'threading': 'مدمج مع Python',
        'datetime': 'مدمج مع Python',
        'os': 'مدمج مع Python',
        'logging': 'مدمج مع Python'
    }

    for module, desc in basic_modules.items():
        try:
            __import__(module)
            print(f"✅ {module} - {desc}")
        except ImportError as e:
            print(f"❌ {module} - خطأ: {e}")

    # المكتبات الخارجية
    external_modules = {
        'requests': 'pip install requests',
        'customtkinter': 'pip install customtkinter',
        'PIL': 'pip install Pillow',
        # 'ayrshare': 'نستخدم عميل مخصوص',
        'dateutil': 'pip install python-dateutil'
    }

    for module, install_cmd in external_modules.items():
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module} - شغل: {install_cmd}")

def check_project_structure():
    """فحص هيكل المشروع"""
    print("\n📁 فحص هيكل المشروع:")

    required_files = [
        'main.py',
        'requirements.txt',
        'utils/__init__.py',
        'utils/config.py',
        'utils/logger.py',
        'database/__init__.py',
        'database/database.py',
        'database/models.py',
        'api/__init__.py',
        'api/ayrshare_client.py',
        'api/rate_limiter.py',
        'gui/__init__.py',
        'gui/main_window.py'
    ]

    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)

    return len(missing_files) == 0

def test_imports_detailed():
    """اختبار مفصل للاستيرادات"""
    print("\n🔍 اختبار مفصل للاستيرادات:")

    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

        # اختبار utils
        try:
            from utils.config import config
            print("✅ utils.config")
        except Exception as e:
            print(f"❌ utils.config - {e}")
            traceback.print_exc()

        try:
            from utils.logger import logger
            print("✅ utils.logger")
        except Exception as e:
            print(f"❌ utils.logger - {e}")
            traceback.print_exc()

        # اختبار database
        try:
            from database.models import Post, ScheduledPost
            print("✅ database.models")
        except Exception as e:
            print(f"❌ database.models - {e}")
            traceback.print_exc()

        try:
            from database.database import db
            print("✅ database.database")
        except Exception as e:
            print(f"❌ database.database - {e}")
            traceback.print_exc()

        # اختبار api
        try:
            from api.rate_limiter import rate_limiter
            print("✅ api.rate_limiter")
        except Exception as e:
            print(f"❌ api.rate_limiter - {e}")
            traceback.print_exc()

        try:
            from api.simple_ayrshare_client import ayrshare_client
            print("✅ api.simple_ayrshare_client")
        except Exception as e:
            print(f"❌ api.simple_ayrshare_client - {e}")
            traceback.print_exc()

        # اختبار gui
        try:
            from gui.main_window import MainWindow
            print("✅ gui.main_window")
        except Exception as e:
            print(f"❌ gui.main_window - {e}")
            traceback.print_exc()

        return True

    except Exception as e:
        print(f"❌ خطأ عام في الاستيرادات: {e}")
        traceback.print_exc()
        return False

def run_app():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل التطبيق:")

    try:
        # استيراد وتشغيل التطبيق
        from gui.main_window import MainWindow

        print("✅ تم استيراد MainWindow")

        app = MainWindow()
        print("✅ تم إنشاء التطبيق")

        print("🎉 بدء تشغيل الواجهة الرسومية...")
        app.run()

    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        traceback.print_exc()

        print("\n🔧 نصائح لحل المشكلة:")
        print("1. تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
        print("2. تحقق من إصدار Python: python --version")
        print("3. راجع ملفات السجل في مجلد logs/")
        print("4. شغل الاختبار البسيط: python simple_test.py")

def main():
    """الدالة الرئيسية"""
    print("🔧 Ayrshare Social Media Manager - وضع المطور")
    print("=" * 60)

    # فحص Python
    check_python_version()

    # فحص المكتبات
    check_imports()

    # فحص هيكل المشروع
    structure_ok = check_project_structure()

    if not structure_ok:
        print("\n❌ هيكل المشروع غير مكتمل")
        return

    # اختبار مفصل للاستيرادات
    imports_ok = test_imports_detailed()

    if not imports_ok:
        print("\n❌ فشل في اختبار الاستيرادات")
        print("💡 شغل: pip install -r requirements.txt")
        return

    print("\n✅ جميع الفحوصات نجحت!")

    # تشغيل التطبيق
    run_app()

if __name__ == "__main__":
    main()
