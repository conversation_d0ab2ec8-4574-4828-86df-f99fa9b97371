@echo off
chcp 65001 >nul
title Ayrshare App Installer

echo ========================================
echo    تثبيت Ayrshare Social Media Manager
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo.
    echo يرجى تثبيت Python 3.10 أو أحدث من:
    echo https://python.org/downloads
    echo.
    echo تأكد من إضافة Python إلى PATH أثناء التثبيت
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: pip غير متوفر
    echo يرجى إعادة تثبيت Python مع pip
    pause
    exit /b 1
)

echo ✅ تم العثور على pip
pip --version

echo.
echo 📦 جاري تثبيت المتطلبات...
echo.

REM تحديث pip
python -m pip install --upgrade pip

REM تثبيت المتطلبات
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ❌ فشل في تثبيت بعض المتطلبات
    echo.
    echo جاري المحاولة مع خيارات إضافية...
    pip install -r requirements.txt --user --upgrade
    
    if errorlevel 1 (
        echo.
        echo ❌ فشل في التثبيت
        echo.
        echo يرجى تجربة الأوامر التالية يدوياً:
        echo pip install --upgrade pip
        echo pip install social-post-api
        echo pip install customtkinter
        echo pip install Pillow
        echo pip install requests
        echo pip install python-dateutil
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ✅ تم تثبيت جميع المتطلبات بنجاح!
echo.

REM تشغيل الاختبارات
echo 🧪 جاري تشغيل الاختبارات...
python test_app.py

echo.
echo 🎉 التثبيت مكتمل!
echo.
echo للتشغيل:
echo - اضغط مرتين على run.bat
echo - أو شغل الأمر: python main.py
echo.
echo للحصول على API Key:
echo 1. اذهب إلى: https://app.ayrshare.com
echo 2. أنشئ حساب أو سجل دخول
echo 3. اذهب إلى قسم API واحصل على المفتاح
echo 4. اربط حساباتك على Facebook, Instagram, TikTok
echo.
pause
