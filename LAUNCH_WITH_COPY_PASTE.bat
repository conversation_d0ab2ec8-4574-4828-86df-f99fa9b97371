@echo off
chcp 65001 >nul
title 🚀 Ayrshare Social Media Manager - مع النسخ واللصق

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║           🚀 Ayrshare Social Media Manager 🚀                ║
echo ║                                                              ║
echo ║              برنامج إدارة وسائل التواصل الاجتماعي           ║
echo ║                  مع دعم النسخ واللصق الكامل                 ║
echo ║                                                              ║
echo ║    📘 Facebook  📸 Instagram  🎵 TikTok  🐦 Twitter/X        ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📋 ميزات النسخ واللصق الجديدة:
echo ✅ نسخ ولصق في مربع النص الرئيسي
echo ✅ نسخ ولصق في حقل الهاشتاجات
echo ✅ قائمة السياق بالنقر الأيمن
echo ✅ جميع الاختصارات المعيارية (Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+A)
echo ✅ رسائل تأكيد في شريط الحالة
echo.
echo ⌨️ الاختصارات المتاحة:
echo • Ctrl+C = نسخ النص المحدد
echo • Ctrl+V = لصق النص من الحافظة
echo • Ctrl+X = قص النص المحدد
echo • Ctrl+A = تحديد جميع النص
echo • النقر الأيمن = قائمة السياق
echo.
echo 🔄 تشغيل التطبيق مع النسخ واللصق...
echo.

REM تشغيل التطبيق
python main.py

REM في حالة الخطأ
if errorlevel 1 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo.
    echo 🧪 هل تريد اختبار النسخ واللصق؟
    set /p test_choice="اكتب 'y' لاختبار النسخ واللصق: "
    if /i "%test_choice%"=="y" (
        echo 🧪 تشغيل اختبار النسخ واللصق...
        python test_copy_paste.py
    )
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق التطبيق بنجاح
    echo 📋 النسخ واللصق يعمل بشكل مثالي!
    echo.
)
