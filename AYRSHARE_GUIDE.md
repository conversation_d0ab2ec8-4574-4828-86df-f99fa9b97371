# 📚 دليل استخدام Ayrshare API - محدث

## 🎯 نظرة عامة

تم تحديث التطبيق ليتوافق مع **وثائق Ayrshare API الرسمية** من:
- https://www.ayrshare.com/docs/introduction
- https://www.ayrshare.com/docs/quickstart

## 🌐 المنصات المدعومة (13 منصة)

### ✅ المنصات الرئيسية المدعومة في التطبيق:
- **Facebook** (`facebook`) - للصفحات التجارية
- **Instagram** (`instagram`) - للحسابات التجارية  
- **TikTok** (`tiktok`) - للحسابات التجارية
- **Twitter/X** (`twitter`) - للحسابات الشخصية والتجارية

### 🔄 منصات إضافية متاحة في Ayrshare:
- **LinkedIn** (`linkedin`)
- **Pinterest** (`pinterest`) 
- **YouTube** (`youtube`)
- **Snapchat** (`snapchat`)
- **Telegram** (`telegram`)
- **Reddit** (`reddit`)
- **Threads** (`threads`)
- **Bluesky** (`bluesky`)
- **Google Business** (`gmb`)

## 🔑 الحصول على API Key

### الخطوات:
1. **اذهب إلى:** https://app.ayrshare.com
2. **أنشئ حساب** أو سجل دخول
3. **اذهب إلى صفحة "API Key"** في اللوحة الجانبية
4. **انسخ API Key** الخاص بك
5. **اربط حساباتك** على المنصات المطلوبة

### 🔐 تنسيق الـ Header:
```
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

## 📡 API Endpoints المحدثة

### 🌐 Base URL الصحيح:
```
https://api.ayrshare.com/api/
```

### 📝 نشر منشور:
```
POST /post
```

### 📊 الحصول على التاريخ:
```
GET /history
```

### 🗑️ حذف منشور:
```
DELETE /delete
```

### 📤 رفع ملف:
```
POST /upload
```

### 👤 معلومات المستخدم:
```
GET /user
```

## 🚀 أمثلة الاستخدام

### 1. نشر نص بسيط:
```json
{
  "post": "مرحباً بالعالم!",
  "platforms": ["facebook", "instagram", "twitter", "tiktok"]
}
```

### 2. نشر مع صورة:
```json
{
  "post": "شاهدوا هذه الصورة الرائعة!",
  "platforms": ["facebook", "instagram"],
  "mediaUrls": ["https://example.com/image.jpg"]
}
```

### 3. نشر مجدول:
```json
{
  "post": "منشور مجدول",
  "platforms": ["facebook", "twitter"],
  "scheduleDate": "2024-12-31T12:00:00Z"
}
```

### 4. نشر فيديو:
```json
{
  "post": "فيديو جديد!",
  "platforms": ["tiktok", "instagram"],
  "mediaUrls": ["https://example.com/video.mp4"],
  "isVideo": true
}
```

## 📋 معايير الوسائط

### 🖼️ الصور:
- **الصيغ:** JPG, PNG, GIF, WEBP
- **الحد الأقصى:** 10 MB
- **الأبعاد:** حسب كل منصة

### 🎥 الفيديوهات:
- **الصيغ:** MP4, MOV, AVI
- **الحد الأقصى:** 100 MB (حسب الخطة)
- **المدة:** حسب كل منصة

### 📱 متطلبات خاصة:
- **TikTok:** فيديو عمودي (9:16)
- **Instagram Reels:** فيديو عمودي
- **Facebook/Instagram Stories:** فيديو عمودي

## 🔧 ميزات متقدمة

### 🤖 النشر التلقائي:
```json
{
  "randomPost": true,
  "randomMediaUrl": true,
  "platforms": ["facebook", "twitter"]
}
```

### 🔗 تقصير الروابط:
```json
{
  "post": "تحقق من هذا الرابط: https://example.com",
  "platforms": ["twitter"],
  "shortenLinks": true
}
```

### 🔄 إعادة النشر التلقائي:
```json
{
  "post": "محتوى دائم الخضرة",
  "platforms": ["facebook"],
  "autoRepost": {
    "repeat": 3,
    "days": 7,
    "startDateTime": "2024-12-01T12:00:00Z"
  }
}
```

### 💬 تعطيل التعليقات:
```json
{
  "post": "منشور بدون تعليقات",
  "platforms": ["instagram", "linkedin"],
  "disableComments": true
}
```

## 📊 استجابة API

### ✅ استجابة ناجحة:
```json
{
  "status": "success",
  "errors": [],
  "postIds": [
    {
      "status": "success",
      "id": "123456789",
      "postUrl": "https://facebook.com/post/123456789",
      "platform": "facebook"
    }
  ],
  "id": "AyrsharePostID123"
}
```

### ❌ استجابة خطأ:
```json
{
  "status": "error",
  "message": "وصف الخطأ",
  "errors": ["تفاصيل الخطأ"]
}
```

## 🛠️ استكشاف الأخطاء

### الأخطاء الشائعة:

#### 1. **401 Unauthorized**
- **السبب:** API Key غير صحيح
- **الحل:** تحقق من API Key في Dashboard

#### 2. **400 Bad Request**
- **السبب:** بيانات غير صحيحة
- **الحل:** تحقق من تنسيق JSON والحقول المطلوبة

#### 3. **403 Forbidden**
- **السبب:** المنصة غير مربوطة
- **الحل:** اربط المنصة في Dashboard

#### 4. **429 Too Many Requests**
- **السبب:** تجاوز حدود المعدل
- **الحل:** انتظر أو فعل تخطي الحدود

## 📈 أفضل الممارسات

### 🔐 الأمان:
- احتفظ بـ API Key آمناً
- لا تشارك المفتاح في الكود العام
- استخدم متغيرات البيئة

### ⚡ الأداء:
- استخدم Batch Posts للنشر المتعدد
- فعل تخطي الحدود عند الحاجة
- راقب استجابات API

### 📝 المحتوى:
- اتبع إرشادات كل منصة
- استخدم الهاشتاجات المناسبة
- اختبر المحتوى قبل النشر

## 🧪 الاختبار

### 🎲 نشر تجريبي:
```json
{
  "randomPost": true,
  "randomMediaUrl": true,
  "platforms": ["facebook"],
  "isPortraitVideo": true
}
```

### 🔍 أدوات الاختبار:
- **Postman** لاختبار API
- **cURL** للاختبار السريع
- **التطبيق المدمج** للاختبار الشامل

## 📞 الدعم

### 🆘 الحصول على المساعدة:
- **الوثائق:** https://www.ayrshare.com/docs
- **الدعم:** chat في Dashboard
- **البريد الإلكتروني:** <EMAIL>
- **GitHub:** https://github.com/ayrshare

### 📊 حالة النظام:
- **مراقبة:** https://www.ayrshare.com/docs/additional/status

## 🎯 الخلاصة

تم تحديث التطبيق ليتوافق مع:
- ✅ **API URL الصحيح:** `https://api.ayrshare.com/api/`
- ✅ **أسماء المنصات الصحيحة:** `twitter`, `facebook`, `instagram`, `tiktok`
- ✅ **تنسيق الطلبات الصحيح:** حسب الوثائق الرسمية
- ✅ **معالجة الاستجابات:** حسب المعايير الرسمية
- ✅ **ميزات متقدمة:** جدولة، وسائط، خيارات خاصة

**التطبيق جاهز للاستخدام مع Ayrshare API!** 🚀
