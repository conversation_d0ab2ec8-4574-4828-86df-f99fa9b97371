# 🚀 Ayrshare Social Media Manager

<div align="center">

![Ayrshare Logo](https://img.shields.io/badge/Ayrshare-Social%20Media%20Manager-blue?style=for-the-badge)
![Python](https://img.shields.io/badge/Python-3.8+-green?style=for-the-badge&logo=python)
![License](https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge)

**برنامج إدارة وسائل التواصل الاجتماعي باستخدام Ayrshare API**
**مع واجهة رسومية متقدمة باللغة العربية**

[📖 دليل البدء السريع](QUICK_START.md) • [❓ أسئلة شائعة](FAQ.md) • [✨ الميزات](FEATURES.md) • [📝 سجل التغييرات](CHANGELOG.md)

</div>

---

## الميزات الرئيسية

### 🚀 النشر المتقدم
- نشر المحتوى على Facebook, Instagram, TikTok
- دعم الصور والفيديوهات والنصوص
- نشر الريلز والستوريز
- إضافة الهاشتاجات تلقائياً
- معاينة المنشورات قبل النشر

### ⏰ الجدولة الذكية
- جدولة المنشورات لأوقات محددة
- تكرار المنشورات (يومي، أسبوعي، شهري)
- إدارة قائمة المنشورات المجدولة
- نشر تلقائي في الأوقات المحددة

### 🔄 تخطي حدود المنصات
- تجاوز حدود النشر للمنصات
- تأخير ذكي بين المنشورات
- مراقبة استخدام API
- إحصائيات مفصلة للاستخدام

### 💾 إدارة البيانات
- حفظ الإعدادات تلقائياً
- قاعدة بيانات محلية للمنشورات
- تتبع تاريخ النشر
- نظام سجلات مفصل

## متطلبات التشغيل

### Python 3.10+
```bash
python --version
```

### المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd ayrshare-social-manager
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. الحصول على API Key
1. سجل في [Ayrshare](https://app.ayrshare.com)
2. احصل على API Key من لوحة التحكم
3. (اختياري) احصل على Profile Key للحسابات المتعددة

### 4. تشغيل البرنامج
```bash
python main.py
```

## كيفية الاستخدام

### الإعداد الأولي

1. **إدخال بيانات API:**
   - افتح تبويب "نشر محتوى"
   - أدخل API Key في الحقل المخصص
   - (اختياري) أدخل Profile Key
   - اضغط "اختبار" للتأكد من الاتصال

2. **ربط الحسابات:**
   - اذهب إلى [Ayrshare Dashboard](https://app.ayrshare.com)
   - اربط حساباتك على Facebook, Instagram, TikTok

### النشر الفوري

1. **كتابة المحتوى:**
   - اكتب نص المنشور في المربع المخصص
   - أضف الهاشتاجات في الحقل المناسب

2. **إضافة الوسائط:**
   - اضغط "إضافة صور" لاختيار الصور
   - اضغط "إضافة فيديو" لاختيار فيديو
   - يمكن إضافة عدة ملفات

3. **اختيار المنصات:**
   - حدد المنصات المطلوبة (Facebook, Instagram, TikTok)

4. **النشر:**
   - اضغط "نشر الآن" للنشر الفوري
   - أو "حفظ كمسودة" للحفظ بدون نشر

### الجدولة

1. **تفعيل الجدولة:**
   - ضع علامة على "جدولة المنشور"
   - أدخل التاريخ والوقت المطلوب

2. **إدارة المنشورات المجدولة:**
   - اذهب إلى تبويب "الجدولة"
   - شاهد قائمة المنشورات المجدولة
   - احذف أو عدل المنشورات حسب الحاجة

### الإعدادات المتقدمة

1. **تخطي حدود المنصات:**
   - اذهب إلى تبويب "الإعدادات"
   - فعل "تخطي حدود المنصات"
   - اضبط التأخير بين المنشورات

2. **الهاشتاجات الافتراضية:**
   - اضبط هاشتاجات افتراضية لكل منصة
   - ستضاف تلقائياً للمنشورات الجديدة

3. **حفظ الإعدادات:**
   - اضغط "حفظ الإعدادات" لحفظ التغييرات
   - أو "استعادة الافتراضية" للعودة للإعدادات الأصلية

### مراقبة الإحصائيات

- اذهب إلى تبويب "الإحصائيات"
- شاهد استخدامك الحالي لكل منصة
- تتبع الحدود الساعية واليومية
- اضغط "تحديث الإحصائيات" للحصول على أحدث البيانات

## حل المشاكل الشائعة

### خطأ في الاتصال بـ API
- تأكد من صحة API Key
- تحقق من اتصال الإنترنت
- تأكد من ربط الحسابات في Ayrshare Dashboard

### فشل رفع الملفات
- تأكد من حجم الملف (أقل من 100 ميجابايت)
- تحقق من صيغة الملف المدعومة
- تأكد من وجود مساحة كافية على القرص

### مشاكل الجدولة
- تأكد من صحة التاريخ والوقت
- تحقق من تشغيل البرنامج في الوقت المحدد
- راجع سجلات الأخطاء في مجلد logs

### تخطي الحدود لا يعمل
- تأكد من تفعيل الميزة في الإعدادات
- اضبط تأخير مناسب بين المنشورات
- راقب الإحصائيات للتأكد من الاستخدام

## هيكل المشروع

```
ayrshare-social-manager/
├── main.py                 # الملف الرئيسي
├── requirements.txt        # المتطلبات
├── README.md              # دليل الاستخدام
├── config.json            # ملف الإعدادات (ينشأ تلقائياً)
├── ayrshare_app.db        # قاعدة البيانات (تنشأ تلقائياً)
├── api/                   # مجلد API
│   ├── ayrshare_client.py # عميل Ayrshare
│   └── rate_limiter.py    # إدارة الحدود
├── database/              # مجلد قاعدة البيانات
│   ├── database.py        # إدارة قاعدة البيانات
│   └── models.py          # نماذج البيانات
├── gui/                   # مجلد الواجهة الرسومية
│   └── main_window.py     # النافذة الرئيسية
├── utils/                 # مجلد الأدوات المساعدة
│   ├── config.py          # إدارة الإعدادات
│   └── logger.py          # نظام السجلات
└── logs/                  # مجلد السجلات (ينشأ تلقائياً)
```

## الدعم والمساعدة

### السجلات
- تحقق من ملفات السجل في مجلد `logs/`
- السجلات تحتوي على تفاصيل الأخطاء والعمليات

### الإعدادات
- ملف `config.json` يحتوي على جميع الإعدادات
- يمكن تعديله يدوياً إذا لزم الأمر

### قاعدة البيانات
- ملف `ayrshare_app.db` يحتوي على بيانات المنشورات
- يمكن نسخه احتياطياً للحفاظ على البيانات

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## المساهمة

نرحب بالمساهمات والتحسينات. يرجى إنشاء Pull Request أو Issue للمناقشة.

---

**ملاحظة:** هذا البرنامج يستخدم Ayrshare API ويتطلب اشتراك صالح في الخدمة.
