# -*- coding: utf-8 -*-
"""
نافذة الإعدادات المتقدمة
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from typing import Optional
import threading

from utils.config import config
from utils.logger import logger
from utils.notifications import notification_manager
from utils.updater import update_manager
from utils.backup import backup_manager
from utils.analytics import analytics_manager

class AdvancedSettingsWindow:
    """نافذة الإعدادات المتقدمة"""

    def __init__(self, parent):
        self.parent = parent
        self.window = None
        self.setup_window()

    def setup_window(self):
        """إعداد النافذة"""
        self.window = ctk.CTkToplevel(self.parent)
        self.window.title("الإعدادات المتقدمة")
        self.window.geometry("800x600")
        self.window.resizable(True, True)

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

        # إنشاء التبويبات
        self.notebook = ctk.CTkTabview(self.window)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # تبويبات الإعدادات
        self.create_notifications_tab()
        self.create_backup_tab()
        self.create_updates_tab()
        self.create_analytics_tab()
        self.create_advanced_tab()

        # أزرار التحكم
        self.create_control_buttons()

    def create_notifications_tab(self):
        """تبويب إعدادات الإشعارات"""
        notifications_tab = self.notebook.add("الإشعارات")

        # إطار الإعدادات العامة
        general_frame = ctk.CTkFrame(notifications_tab)
        general_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(general_frame, text="إعدادات الإشعارات العامة",
                    font=("Arial", 16, "bold")).pack(pady=10)

        # تفعيل الإشعارات
        self.notifications_enabled_var = tk.BooleanVar(
            value=config.get('notifications.enabled', True)
        )
        ctk.CTkCheckBox(general_frame, text="تفعيل الإشعارات",
                       variable=self.notifications_enabled_var).pack(anchor="w", padx=10, pady=5)

        # إشعارات سطح المكتب
        self.desktop_notifications_var = tk.BooleanVar(
            value=config.get('notifications.desktop_enabled', True)
        )
        ctk.CTkCheckBox(general_frame, text="إشعارات سطح المكتب",
                       variable=self.desktop_notifications_var).pack(anchor="w", padx=10, pady=5)

        # أصوات الإشعارات
        self.sound_notifications_var = tk.BooleanVar(
            value=config.get('notifications.sound_enabled', True)
        )
        ctk.CTkCheckBox(general_frame, text="أصوات الإشعارات",
                       variable=self.sound_notifications_var).pack(anchor="w", padx=10, pady=5)

        # إطار أنواع الإشعارات
        types_frame = ctk.CTkFrame(notifications_tab)
        types_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(types_frame, text="أنواع الإشعارات",
                    font=("Arial", 14, "bold")).pack(pady=5)

        # إشعارات النشر
        self.post_notifications_var = tk.BooleanVar(
            value=config.get('notifications.post_success', True)
        )
        ctk.CTkCheckBox(types_frame, text="إشعارات نجاح النشر",
                       variable=self.post_notifications_var).pack(anchor="w", padx=10, pady=2)

        # إشعارات الأخطاء
        self.error_notifications_var = tk.BooleanVar(
            value=config.get('notifications.errors', True)
        )
        ctk.CTkCheckBox(types_frame, text="إشعارات الأخطاء",
                       variable=self.error_notifications_var).pack(anchor="w", padx=10, pady=2)

        # إشعارات الجدولة
        self.schedule_notifications_var = tk.BooleanVar(
            value=config.get('notifications.schedule_reminders', True)
        )
        ctk.CTkCheckBox(types_frame, text="تذكيرات الجدولة",
                       variable=self.schedule_notifications_var).pack(anchor="w", padx=10, pady=2)

        # زر اختبار الإشعارات
        ctk.CTkButton(notifications_tab, text="اختبار الإشعارات",
                     command=self.test_notifications).pack(pady=10)

    def create_backup_tab(self):
        """تبويب إعدادات النسخ الاحتياطي"""
        backup_tab = self.notebook.add("النسخ الاحتياطي")

        # إطار الإعدادات العامة
        general_frame = ctk.CTkFrame(backup_tab)
        general_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(general_frame, text="إعدادات النسخ الاحتياطي",
                    font=("Arial", 16, "bold")).pack(pady=10)

        # تفعيل النسخ التلقائي
        self.auto_backup_var = tk.BooleanVar(
            value=config.get('backup.auto_enabled', True)
        )
        ctk.CTkCheckBox(general_frame, text="نسخ احتياطي تلقائي",
                       variable=self.auto_backup_var).pack(anchor="w", padx=10, pady=5)

        # فترة النسخ الاحتياطي
        interval_frame = ctk.CTkFrame(general_frame)
        interval_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(interval_frame, text="فترة النسخ الاحتياطي (ساعات):",
                    width=200).pack(side="left", padx=5)
        self.backup_interval_var = tk.StringVar(
            value=str(config.get('backup.interval_hours', 24))
        )
        ctk.CTkEntry(interval_frame, textvariable=self.backup_interval_var,
                    width=100).pack(side="left", padx=5)

        # عدد النسخ المحفوظة
        max_backups_frame = ctk.CTkFrame(general_frame)
        max_backups_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(max_backups_frame, text="عدد النسخ المحفوظة:",
                    width=200).pack(side="left", padx=5)
        self.max_backups_var = tk.StringVar(
            value=str(config.get('backup.max_backups', 10))
        )
        ctk.CTkEntry(max_backups_frame, textvariable=self.max_backups_var,
                    width=100).pack(side="left", padx=5)

        # ضغط النسخ الاحتياطية
        self.compress_backups_var = tk.BooleanVar(
            value=config.get('backup.compress', True)
        )
        ctk.CTkCheckBox(general_frame, text="ضغط النسخ الاحتياطية",
                       variable=self.compress_backups_var).pack(anchor="w", padx=10, pady=5)

        # إطار العمليات
        operations_frame = ctk.CTkFrame(backup_tab)
        operations_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(operations_frame, text="عمليات النسخ الاحتياطي",
                    font=("Arial", 14, "bold")).pack(pady=5)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(operations_frame)
        buttons_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkButton(buttons_frame, text="إنشاء نسخة احتياطية",
                     command=self.create_backup).pack(side="left", padx=5)
        ctk.CTkButton(buttons_frame, text="استعادة نسخة احتياطية",
                     command=self.restore_backup).pack(side="left", padx=5)
        ctk.CTkButton(buttons_frame, text="إدارة النسخ الاحتياطية",
                     command=self.manage_backups).pack(side="left", padx=5)

    def create_updates_tab(self):
        """تبويب إعدادات التحديثات"""
        updates_tab = self.notebook.add("التحديثات")

        # إطار الإعدادات العامة
        general_frame = ctk.CTkFrame(updates_tab)
        general_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(general_frame, text="إعدادات التحديثات",
                    font=("Arial", 16, "bold")).pack(pady=10)

        # تفعيل فحص التحديثات التلقائي
        self.auto_updates_var = tk.BooleanVar(
            value=config.get('updates.auto_check', True)
        )
        ctk.CTkCheckBox(general_frame, text="فحص التحديثات تلقائياً",
                       variable=self.auto_updates_var).pack(anchor="w", padx=10, pady=5)

        # فترة فحص التحديثات
        check_interval_frame = ctk.CTkFrame(general_frame)
        check_interval_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(check_interval_frame, text="فترة فحص التحديثات (ساعات):",
                    width=200).pack(side="left", padx=5)
        self.update_interval_var = tk.StringVar(
            value=str(config.get('updates.check_interval_hours', 24))
        )
        ctk.CTkEntry(check_interval_frame, textvariable=self.update_interval_var,
                    width=100).pack(side="left", padx=5)

        # معلومات الإصدار الحالي
        version_frame = ctk.CTkFrame(updates_tab)
        version_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(version_frame, text="معلومات الإصدار",
                    font=("Arial", 14, "bold")).pack(pady=5)

        current_version = config.get('app.version', '1.0.0')
        ctk.CTkLabel(version_frame, text=f"الإصدار الحالي: {current_version}").pack(anchor="w", padx=10)

        last_check = config.get('updates.last_check')
        if last_check:
            from datetime import datetime
            last_check_time = datetime.fromisoformat(last_check)
            last_check_str = last_check_time.strftime('%Y-%m-%d %H:%M')
        else:
            last_check_str = "لم يتم الفحص بعد"

        ctk.CTkLabel(version_frame, text=f"آخر فحص: {last_check_str}").pack(anchor="w", padx=10)

        # أزرار التحديثات
        buttons_frame = ctk.CTkFrame(updates_tab)
        buttons_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkButton(buttons_frame, text="فحص التحديثات الآن",
                     command=self.check_updates).pack(side="left", padx=5)
        ctk.CTkButton(buttons_frame, text="تاريخ التحديثات",
                     command=self.show_update_history).pack(side="left", padx=5)

    def create_analytics_tab(self):
        """تبويب إعدادات الإحصائيات"""
        analytics_tab = self.notebook.add("الإحصائيات")

        # إطار الإعدادات العامة
        general_frame = ctk.CTkFrame(analytics_tab)
        general_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(general_frame, text="إعدادات الإحصائيات",
                    font=("Arial", 16, "bold")).pack(pady=10)

        # تفعيل جمع الإحصائيات
        self.analytics_enabled_var = tk.BooleanVar(
            value=config.get('analytics.enabled', True)
        )
        ctk.CTkCheckBox(general_frame, text="تفعيل جمع الإحصائيات",
                       variable=self.analytics_enabled_var).pack(anchor="w", padx=10, pady=5)

        # إحصائيات مفصلة
        self.detailed_analytics_var = tk.BooleanVar(
            value=config.get('analytics.detailed', True)
        )
        ctk.CTkCheckBox(general_frame, text="إحصائيات مفصلة",
                       variable=self.detailed_analytics_var).pack(anchor="w", padx=10, pady=5)

        # فترة الاحتفاظ بالإحصائيات
        retention_frame = ctk.CTkFrame(general_frame)
        retention_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(retention_frame, text="فترة الاحتفاظ بالإحصائيات (أيام):",
                    width=250).pack(side="left", padx=5)
        self.analytics_retention_var = tk.StringVar(
            value=str(config.get('analytics.retention_days', 90))
        )
        ctk.CTkEntry(retention_frame, textvariable=self.analytics_retention_var,
                    width=100).pack(side="left", padx=5)

        # إطار العمليات
        operations_frame = ctk.CTkFrame(analytics_tab)
        operations_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(operations_frame, text="عمليات الإحصائيات",
                    font=("Arial", 14, "bold")).pack(pady=5)

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(operations_frame)
        buttons_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkButton(buttons_frame, text="تصدير الإحصائيات",
                     command=self.export_analytics).pack(side="left", padx=5)
        ctk.CTkButton(buttons_frame, text="مسح الإحصائيات",
                     command=self.clear_analytics).pack(side="left", padx=5)
        ctk.CTkButton(buttons_frame, text="عرض التقرير",
                     command=self.show_analytics_report).pack(side="left", padx=5)

    def create_advanced_tab(self):
        """تبويب الإعدادات المتقدمة"""
        advanced_tab = self.notebook.add("متقدم")

        # إطار إعدادات الأداء
        performance_frame = ctk.CTkFrame(advanced_tab)
        performance_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(performance_frame, text="إعدادات الأداء",
                    font=("Arial", 16, "bold")).pack(pady=10)

        # عدد الخيوط المتزامنة
        threads_frame = ctk.CTkFrame(performance_frame)
        threads_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(threads_frame, text="عدد الخيوط المتزامنة:",
                    width=200).pack(side="left", padx=5)
        self.max_threads_var = tk.StringVar(
            value=str(config.get('performance.max_threads', 4))
        )
        ctk.CTkEntry(threads_frame, textvariable=self.max_threads_var,
                    width=100).pack(side="left", padx=5)

        # مهلة الاتصال
        timeout_frame = ctk.CTkFrame(performance_frame)
        timeout_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(timeout_frame, text="مهلة الاتصال (ثانية):",
                    width=200).pack(side="left", padx=5)
        self.timeout_var = tk.StringVar(
            value=str(config.get('api.timeout_seconds', 30))
        )
        ctk.CTkEntry(timeout_frame, textvariable=self.timeout_var,
                    width=100).pack(side="left", padx=5)

        # إطار إعدادات التطوير
        dev_frame = ctk.CTkFrame(advanced_tab)
        dev_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(dev_frame, text="إعدادات التطوير",
                    font=("Arial", 14, "bold")).pack(pady=5)

        # وضع التطوير
        self.debug_mode_var = tk.BooleanVar(
            value=config.get('debug.enabled', False)
        )
        ctk.CTkCheckBox(dev_frame, text="وضع التطوير (Debug Mode)",
                       variable=self.debug_mode_var).pack(anchor="w", padx=10, pady=5)

        # سجلات مفصلة
        self.verbose_logging_var = tk.BooleanVar(
            value=config.get('logging.verbose', False)
        )
        ctk.CTkCheckBox(dev_frame, text="سجلات مفصلة",
                       variable=self.verbose_logging_var).pack(anchor="w", padx=10, pady=5)

        # إطار عمليات النظام
        system_frame = ctk.CTkFrame(advanced_tab)
        system_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(system_frame, text="عمليات النظام",
                    font=("Arial", 14, "bold")).pack(pady=5)

        # أزرار العمليات
        system_buttons_frame = ctk.CTkFrame(system_frame)
        system_buttons_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkButton(system_buttons_frame, text="مسح ذاكرة التخزين المؤقت",
                     command=self.clear_cache).pack(side="left", padx=5)
        ctk.CTkButton(system_buttons_frame, text="إعادة تعيين الإعدادات",
                     command=self.reset_settings).pack(side="left", padx=5)
        ctk.CTkButton(system_buttons_frame, text="تصدير الإعدادات",
                     command=self.export_settings).pack(side="left", padx=5)

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        control_frame = ctk.CTkFrame(self.window)
        control_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkButton(control_frame, text="حفظ",
                     command=self.save_settings).pack(side="right", padx=5)
        ctk.CTkButton(control_frame, text="إلغاء",
                     command=self.cancel).pack(side="right", padx=5)
        ctk.CTkButton(control_frame, text="تطبيق",
                     command=self.apply_settings).pack(side="right", padx=5)

    # ===== وظائف الإشعارات =====

    def test_notifications(self):
        """اختبار الإشعارات"""
        try:
            notification_manager.show_success("اختبار", "هذا اختبار لإشعار النجاح")
            notification_manager.show_info("اختبار", "هذا اختبار لإشعار المعلومات")
            notification_manager.show_warning("اختبار", "هذا اختبار لإشعار التحذير")
            notification_manager.show_error("اختبار", "هذا اختبار لإشعار الخطأ")

        except Exception as e:
            logger.error(f"خطأ في اختبار الإشعارات: {e}")
            messagebox.showerror("خطأ", f"خطأ في اختبار الإشعارات: {e}")

    # ===== وظائف النسخ الاحتياطي =====

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        def backup_thread():
            try:
                # نافذة التقدم
                progress_window = self.create_progress_window("إنشاء نسخة احتياطية")

                def progress_callback(progress):
                    progress_window.update_progress(progress)

                backup_path = backup_manager.create_backup(progress_callback=progress_callback)

                progress_window.close()

                if backup_path:
                    messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية:\n{backup_path}")
                else:
                    messagebox.showerror("خطأ", "فشل في إنشاء النسخة الاحتياطية")

            except Exception as e:
                logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
                messagebox.showerror("خطأ", f"خطأ في إنشاء النسخة الاحتياطية: {e}")

        threading.Thread(target=backup_thread, daemon=True).start()

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        try:
            # اختيار ملف النسخة الاحتياطية
            file_types = [
                ("ملفات النسخ الاحتياطي", "*.zip"),
                ("جميع الملفات", "*.*")
            ]

            backup_path = filedialog.askopenfilename(
                title="اختر النسخة الاحتياطية",
                filetypes=file_types
            )

            if not backup_path:
                return

            # تأكيد الاستعادة
            if not messagebox.askyesno("تأكيد",
                                     "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n"
                                     "سيتم استبدال البيانات الحالية."):
                return

            def restore_thread():
                try:
                    progress_window = self.create_progress_window("استعادة النسخة الاحتياطية")

                    def progress_callback(progress):
                        progress_window.update_progress(progress)

                    success = backup_manager.restore_backup(backup_path, progress_callback)

                    progress_window.close()

                    if success:
                        messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح")
                    else:
                        messagebox.showerror("خطأ", "فشل في استعادة النسخة الاحتياطية")

                except Exception as e:
                    logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
                    messagebox.showerror("خطأ", f"خطأ في الاستعادة: {e}")

            threading.Thread(target=restore_thread, daemon=True).start()

        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            messagebox.showerror("خطأ", f"خطأ في الاستعادة: {e}")

    def manage_backups(self):
        """إدارة النسخ الاحتياطية"""
        try:
            # نافذة إدارة النسخ الاحتياطية
            manage_window = ctk.CTkToplevel(self.window)
            manage_window.title("إدارة النسخ الاحتياطية")
            manage_window.geometry("600x400")

            # قائمة النسخ الاحتياطية
            backups_frame = ctk.CTkFrame(manage_window)
            backups_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # جدول النسخ الاحتياطية
            columns = ("الاسم", "التاريخ", "الحجم", "النوع")
            backups_tree = ttk.Treeview(backups_frame, columns=columns, show="headings")

            for col in columns:
                backups_tree.heading(col, text=col)
                backups_tree.column(col, width=120)

            backups_tree.pack(fill="both", expand=True, padx=5, pady=5)

            # تحميل قائمة النسخ الاحتياطية
            backups = backup_manager.list_backups()
            for backup in backups:
                backups_tree.insert("", "end", values=(
                    backup['name'],
                    backup['created_at'].strftime('%Y-%m-%d %H:%M'),
                    f"{backup['size_mb']:.1f} MB",
                    backup['type']
                ))

            # أزرار الإدارة
            buttons_frame = ctk.CTkFrame(manage_window)
            buttons_frame.pack(fill="x", padx=10, pady=5)

            def delete_selected():
                selected = backups_tree.selection()
                if not selected:
                    messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية")
                    return

                if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف النسخة الاحتياطية؟"):
                    item = backups_tree.item(selected[0])
                    backup_name = item['values'][0]

                    # البحث عن مسار النسخة الاحتياطية
                    for backup in backups:
                        if backup['name'] == backup_name:
                            if backup_manager.delete_backup(backup['path']):
                                backups_tree.delete(selected[0])
                                messagebox.showinfo("نجح", "تم حذف النسخة الاحتياطية")
                            else:
                                messagebox.showerror("خطأ", "فشل في حذف النسخة الاحتياطية")
                            break

            ctk.CTkButton(buttons_frame, text="حذف المحدد",
                         command=delete_selected).pack(side="left", padx=5)
            ctk.CTkButton(buttons_frame, text="تحديث القائمة",
                         command=lambda: self.refresh_backups_list(backups_tree, backups)).pack(side="left", padx=5)
            ctk.CTkButton(buttons_frame, text="إغلاق",
                         command=manage_window.destroy).pack(side="right", padx=5)

        except Exception as e:
            logger.error(f"خطأ في إدارة النسخ الاحتياطية: {e}")
            messagebox.showerror("خطأ", f"خطأ في إدارة النسخ الاحتياطية: {e}")

    def refresh_backups_list(self, tree, backups_list):
        """تحديث قائمة النسخ الاحتياطية"""
        try:
            # مسح القائمة الحالية
            for item in tree.get_children():
                tree.delete(item)

            # تحميل القائمة الجديدة
            backups = backup_manager.list_backups()
            backups_list.clear()
            backups_list.extend(backups)

            for backup in backups:
                tree.insert("", "end", values=(
                    backup['name'],
                    backup['created_at'].strftime('%Y-%m-%d %H:%M'),
                    f"{backup['size_mb']:.1f} MB",
                    backup['type']
                ))

        except Exception as e:
            logger.error(f"خطأ في تحديث قائمة النسخ الاحتياطية: {e}")

    # ===== وظائف التحديثات =====

    def check_updates(self):
        """فحص التحديثات"""
        def check_thread():
            try:
                has_update, update_info = update_manager.check_for_updates(show_no_updates=True)

                if has_update and update_info:
                    # عرض معلومات التحديث
                    self.show_update_dialog(update_info)
                else:
                    messagebox.showinfo("التحديثات", "لا توجد تحديثات متاحة")

            except Exception as e:
                logger.error(f"خطأ في فحص التحديثات: {e}")
                messagebox.showerror("خطأ", f"خطأ في فحص التحديثات: {e}")

        threading.Thread(target=check_thread, daemon=True).start()

    def show_update_dialog(self, update_info):
        """عرض نافذة معلومات التحديث"""
        try:
            update_window = ctk.CTkToplevel(self.window)
            update_window.title("تحديث متاح")
            update_window.geometry("500x400")

            # معلومات التحديث
            info_frame = ctk.CTkFrame(update_window)
            info_frame.pack(fill="both", expand=True, padx=10, pady=10)

            ctk.CTkLabel(info_frame, text=f"تحديث متاح: الإصدار {update_info['version']}",
                        font=("Arial", 16, "bold")).pack(pady=10)

            ctk.CTkLabel(info_frame, text=f"تاريخ الإصدار: {update_info['release_date']}").pack(anchor="w", padx=10)
            ctk.CTkLabel(info_frame, text=f"حجم التحديث: {update_info['size_mb']} MB").pack(anchor="w", padx=10)

            # قائمة التغييرات
            ctk.CTkLabel(info_frame, text="التغييرات الجديدة:",
                        font=("Arial", 12, "bold")).pack(anchor="w", padx=10, pady=(10, 5))

            changelog_text = ctk.CTkTextbox(info_frame, height=150)
            changelog_text.pack(fill="both", expand=True, padx=10, pady=5)

            for change in update_info['changelog']:
                changelog_text.insert("end", f"• {change}\n")

            changelog_text.configure(state="disabled")

            # أزرار التحكم
            buttons_frame = ctk.CTkFrame(update_window)
            buttons_frame.pack(fill="x", padx=10, pady=5)

            def download_update():
                update_window.destroy()
                self.download_and_install_update(update_info)

            ctk.CTkButton(buttons_frame, text="تحميل وتثبيت",
                         command=download_update).pack(side="right", padx=5)
            ctk.CTkButton(buttons_frame, text="تذكيرني لاحقاً",
                         command=update_window.destroy).pack(side="right", padx=5)

        except Exception as e:
            logger.error(f"خطأ في عرض نافذة التحديث: {e}")

    def download_and_install_update(self, update_info):
        """تحميل وتثبيت التحديث"""
        def update_thread():
            try:
                progress_window = self.create_progress_window("تحميل التحديث")

                def progress_callback(progress):
                    progress_window.update_progress(progress)

                # تحميل التحديث
                success = update_manager.download_update(update_info, progress_callback)

                if success:
                    progress_window.set_text("تثبيت التحديث...")

                    # إنشاء نسخة احتياطية
                    backup_manager.create_backup("pre_update_backup")

                    # تثبيت التحديث
                    install_success = update_manager.install_update(update_info)

                    progress_window.close()

                    if install_success:
                        messagebox.showinfo("نجح", "تم تثبيت التحديث بنجاح!\nسيتم إعادة تشغيل البرنامج.")
                        # إعادة تشغيل البرنامج
                        self.restart_application()
                    else:
                        messagebox.showerror("خطأ", "فشل في تثبيت التحديث")
                else:
                    progress_window.close()
                    messagebox.showerror("خطأ", "فشل في تحميل التحديث")

            except Exception as e:
                logger.error(f"خطأ في تحميل وتثبيت التحديث: {e}")
                messagebox.showerror("خطأ", f"خطأ في التحديث: {e}")

        threading.Thread(target=update_thread, daemon=True).start()

    def show_update_history(self):
        """عرض تاريخ التحديثات"""
        try:
            history = update_manager.get_update_history()

            if not history:
                messagebox.showinfo("تاريخ التحديثات", "لا يوجد تاريخ تحديثات")
                return

            # نافذة تاريخ التحديثات
            history_window = ctk.CTkToplevel(self.window)
            history_window.title("تاريخ التحديثات")
            history_window.geometry("600x400")

            # قائمة التحديثات
            history_text = ctk.CTkTextbox(history_window)
            history_text.pack(fill="both", expand=True, padx=10, pady=10)

            for update in history:
                history_text.insert("end", f"الإصدار: {update['version']}\n")
                history_text.insert("end", f"تاريخ التثبيت: {update['installed_date']}\n")
                history_text.insert("end", "التغييرات:\n")
                for change in update.get('changelog', []):
                    history_text.insert("end", f"  • {change}\n")
                history_text.insert("end", "\n" + "="*50 + "\n\n")

            history_text.configure(state="disabled")

        except Exception as e:
            logger.error(f"خطأ في عرض تاريخ التحديثات: {e}")
            messagebox.showerror("خطأ", f"خطأ في عرض التاريخ: {e}")

    # ===== وظائف الإحصائيات =====

    def export_analytics(self):
        """تصدير الإحصائيات"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="حفظ الإحصائيات",
                defaultextension=".json",
                filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
            )

            if file_path:
                if analytics_manager.export_analytics(file_path):
                    messagebox.showinfo("نجح", f"تم تصدير الإحصائيات إلى:\n{file_path}")
                else:
                    messagebox.showerror("خطأ", "فشل في تصدير الإحصائيات")

        except Exception as e:
            logger.error(f"خطأ في تصدير الإحصائيات: {e}")
            messagebox.showerror("خطأ", f"خطأ في التصدير: {e}")

    def clear_analytics(self):
        """مسح الإحصائيات"""
        try:
            if messagebox.askyesno("تأكيد", "هل أنت متأكد من مسح جميع الإحصائيات؟\nهذا الإجراء لا يمكن التراجع عنه."):
                # مسح قاعدة بيانات الإحصائيات
                import os
                if os.path.exists("analytics.db"):
                    os.remove("analytics.db")
                    analytics_manager.init_analytics_db()
                    messagebox.showinfo("نجح", "تم مسح الإحصائيات")
                else:
                    messagebox.showinfo("معلومات", "لا توجد إحصائيات لمسحها")

        except Exception as e:
            logger.error(f"خطأ في مسح الإحصائيات: {e}")
            messagebox.showerror("خطأ", f"خطأ في المسح: {e}")

    def show_analytics_report(self):
        """عرض تقرير الإحصائيات"""
        try:
            report = analytics_manager.generate_summary_report()

            if not report:
                messagebox.showinfo("الإحصائيات", "لا توجد إحصائيات متاحة")
                return

            # نافذة التقرير
            report_window = ctk.CTkToplevel(self.window)
            report_window.title("تقرير الإحصائيات")
            report_window.geometry("700x500")

            # محتوى التقرير
            report_text = ctk.CTkTextbox(report_window)
            report_text.pack(fill="both", expand=True, padx=10, pady=10)

            # إضافة محتوى التقرير
            summary = report.get('summary', {})
            report_text.insert("end", "📊 ملخص الإحصائيات\n")
            report_text.insert("end", "="*50 + "\n\n")

            report_text.insert("end", f"إجمالي المنشورات: {summary.get('total_posts', 0)}\n")
            report_text.insert("end", f"المنشورات الناجحة: {summary.get('successful_posts', 0)}\n")
            report_text.insert("end", f"المنشورات الفاشلة: {summary.get('failed_posts', 0)}\n")
            report_text.insert("end", f"معدل النجاح: {summary.get('success_rate', 0):.1f}%\n")
            report_text.insert("end", f"متوسط المنشورات يومياً: {summary.get('avg_posts_per_day', 0):.1f}\n\n")

            # إحصائيات المنصات
            platform_stats = report.get('platform_stats', {})
            if platform_stats:
                report_text.insert("end", "📱 إحصائيات المنصات\n")
                report_text.insert("end", "="*30 + "\n\n")

                for platform, stats in platform_stats.items():
                    report_text.insert("end", f"{platform.upper()}:\n")
                    report_text.insert("end", f"  المنشورات: {stats.get('total_posts', 0)}\n")
                    report_text.insert("end", f"  معدل النجاح: {stats.get('success_rate', 0):.1f}%\n")
                    report_text.insert("end", f"  متوسط وقت الاستجابة: {stats.get('avg_response_time', 0):.2f}s\n\n")

            # أنماط النشر
            patterns = report.get('posting_patterns', {})
            if patterns:
                report_text.insert("end", "⏰ أنماط النشر\n")
                report_text.insert("end", "="*20 + "\n\n")

                best_hour = patterns.get('best_posting_hour')
                if best_hour is not None:
                    report_text.insert("end", f"أفضل ساعة للنشر: {best_hour}:00\n")

                most_active_day = patterns.get('most_active_day')
                if most_active_day:
                    report_text.insert("end", f"أكثر الأيام نشاطاً: {most_active_day}\n")

            report_text.configure(state="disabled")

        except Exception as e:
            logger.error(f"خطأ في عرض تقرير الإحصائيات: {e}")
            messagebox.showerror("خطأ", f"خطأ في عرض التقرير: {e}")

    # ===== وظائف الإعدادات المتقدمة =====

    def clear_cache(self):
        """مسح ذاكرة التخزين المؤقت"""
        try:
            import shutil

            cache_dirs = ['__pycache__', 'logs', 'temp']
            cleared_count = 0

            for cache_dir in cache_dirs:
                if os.path.exists(cache_dir):
                    if cache_dir == 'logs':
                        # مسح السجلات القديمة فقط
                        from utils.logger import logger
                        logger.clear_old_logs(7)  # الاحتفاظ بآخر 7 أيام
                    else:
                        shutil.rmtree(cache_dir)
                    cleared_count += 1

            # مسح ملفات Python المؤقتة
            for root, dirs, files in os.walk('.'):
                for file in files:
                    if file.endswith('.pyc') or file.endswith('.pyo'):
                        try:
                            os.remove(os.path.join(root, file))
                            cleared_count += 1
                        except:
                            pass

            messagebox.showinfo("نجح", f"تم مسح ذاكرة التخزين المؤقت\nتم مسح {cleared_count} عنصر")

        except Exception as e:
            logger.error(f"خطأ في مسح ذاكرة التخزين المؤقت: {e}")
            messagebox.showerror("خطأ", f"خطأ في المسح: {e}")

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        try:
            if messagebox.askyesno("تأكيد",
                                 "هل أنت متأكد من إعادة تعيين جميع الإعدادات؟\n"
                                 "سيتم فقدان جميع الإعدادات المخصصة."):

                config.reset_to_default()
                messagebox.showinfo("نجح", "تم إعادة تعيين الإعدادات\nسيتم إعادة تشغيل البرنامج")
                self.restart_application()

        except Exception as e:
            logger.error(f"خطأ في إعادة تعيين الإعدادات: {e}")
            messagebox.showerror("خطأ", f"خطأ في إعادة التعيين: {e}")

    def export_settings(self):
        """تصدير الإعدادات"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="تصدير الإعدادات",
                defaultextension=".json",
                filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
            )

            if file_path:
                if config.export_config(file_path):
                    messagebox.showinfo("نجح", f"تم تصدير الإعدادات إلى:\n{file_path}")
                else:
                    messagebox.showerror("خطأ", "فشل في تصدير الإعدادات")

        except Exception as e:
            logger.error(f"خطأ في تصدير الإعدادات: {e}")
            messagebox.showerror("خطأ", f"خطأ في التصدير: {e}")

    # ===== وظائف مساعدة =====

    def create_progress_window(self, title: str):
        """إنشاء نافذة التقدم"""
        class ProgressWindow:
            def __init__(self, parent, title):
                self.window = ctk.CTkToplevel(parent)
                self.window.title(title)
                self.window.geometry("400x150")
                self.window.resizable(False, False)
                self.window.transient(parent)
                self.window.grab_set()

                # تمركز النافذة
                self.window.update_idletasks()
                x = (self.window.winfo_screenwidth() // 2) - (400 // 2)
                y = (self.window.winfo_screenheight() // 2) - (150 // 2)
                self.window.geometry(f"400x150+{x}+{y}")

                # محتوى النافذة
                self.label = ctk.CTkLabel(self.window, text=title, font=("Arial", 14, "bold"))
                self.label.pack(pady=20)

                self.progress = ctk.CTkProgressBar(self.window, width=300)
                self.progress.pack(pady=10)
                self.progress.set(0)

                self.percent_label = ctk.CTkLabel(self.window, text="0%")
                self.percent_label.pack(pady=5)

            def update_progress(self, value):
                self.progress.set(value / 100)
                self.percent_label.configure(text=f"{value:.1f}%")
                self.window.update()

            def set_text(self, text):
                self.label.configure(text=text)
                self.window.update()

            def close(self):
                self.window.destroy()

        return ProgressWindow(self.window, title)

    def restart_application(self):
        """إعادة تشغيل التطبيق"""
        try:
            import sys
            import subprocess

            # إغلاق النافذة الحالية
            self.window.destroy()

            # إعادة تشغيل البرنامج
            subprocess.Popen([sys.executable] + sys.argv)

            # إنهاء العملية الحالية
            sys.exit(0)

        except Exception as e:
            logger.error(f"خطأ في إعادة تشغيل التطبيق: {e}")
            messagebox.showerror("خطأ", f"خطأ في إعادة التشغيل: {e}")

    # ===== وظائف التحكم =====

    def apply_settings(self):
        """تطبيق الإعدادات"""
        try:
            # حفظ إعدادات الإشعارات
            config.set('notifications.enabled', self.notifications_enabled_var.get())
            config.set('notifications.desktop_enabled', self.desktop_notifications_var.get())
            config.set('notifications.sound_enabled', self.sound_notifications_var.get())
            config.set('notifications.post_success', self.post_notifications_var.get())
            config.set('notifications.errors', self.error_notifications_var.get())
            config.set('notifications.schedule_reminders', self.schedule_notifications_var.get())

            # تطبيق إعدادات الإشعارات
            notification_manager.enable_notifications(self.notifications_enabled_var.get())
            notification_manager.enable_desktop_notifications(self.desktop_notifications_var.get())
            notification_manager.enable_sound(self.sound_notifications_var.get())

            # حفظ إعدادات النسخ الاحتياطي
            config.set('backup.auto_enabled', self.auto_backup_var.get())
            config.set('backup.interval_hours', int(self.backup_interval_var.get()))
            config.set('backup.max_backups', int(self.max_backups_var.get()))
            config.set('backup.compress', self.compress_backups_var.get())

            # تطبيق إعدادات النسخ الاحتياطي
            backup_manager.enable_auto_backup(self.auto_backup_var.get())
            backup_manager.backup_interval_hours = int(self.backup_interval_var.get())
            backup_manager.max_backups = int(self.max_backups_var.get())
            backup_manager.compress_backups = self.compress_backups_var.get()

            # حفظ إعدادات التحديثات
            config.set('updates.auto_check', self.auto_updates_var.get())
            config.set('updates.check_interval_hours', int(self.update_interval_var.get()))

            # تطبيق إعدادات التحديثات
            update_manager.enable_auto_updates(self.auto_updates_var.get())
            update_manager.set_check_interval(int(self.update_interval_var.get()))

            # حفظ إعدادات الإحصائيات
            config.set('analytics.enabled', self.analytics_enabled_var.get())
            config.set('analytics.detailed', self.detailed_analytics_var.get())
            config.set('analytics.retention_days', int(self.analytics_retention_var.get()))

            # حفظ الإعدادات المتقدمة
            config.set('performance.max_threads', int(self.max_threads_var.get()))
            config.set('api.timeout_seconds', int(self.timeout_var.get()))
            config.set('debug.enabled', self.debug_mode_var.get())
            config.set('logging.verbose', self.verbose_logging_var.get())

            messagebox.showinfo("نجح", "تم تطبيق الإعدادات بنجاح")

        except ValueError as e:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة في جميع الحقول")
        except Exception as e:
            logger.error(f"خطأ في تطبيق الإعدادات: {e}")
            messagebox.showerror("خطأ", f"خطأ في تطبيق الإعدادات: {e}")

    def save_settings(self):
        """حفظ الإعدادات وإغلاق النافذة"""
        self.apply_settings()
        self.window.destroy()

    def cancel(self):
        """إلغاء وإغلاق النافذة"""
        self.window.destroy()

    def show(self):
        """عرض النافذة"""
        self.window.mainloop()
