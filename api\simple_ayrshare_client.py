# -*- coding: utf-8 -*-
"""
عميل Ayrshare API مبسط
"""
import time
import os
import requests
from typing import Dict, List, Optional, Any
from datetime import datetime
from utils.logger import logger
from utils.config import config

class SimpleAyrshareClient:
    """عميل مبسط للتعامل مع Ayrshare API"""

    def __init__(self, api_key: Optional[str] = None, profile_key: Optional[str] = None):
        self.api_key = api_key or config.get('api_key', '')
        self.profile_key = profile_key or config.get('profile_key', '')
        self.base_url = "https://api.ayrshare.com/api"
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}' if self.api_key else ''
        }
        # لا نختبر الاتصال تلقائياً عند الإنشاء

    def set_credentials(self, api_key: str, profile_key: Optional[str] = None) -> bool:
        """تعيين بيانات الاعتماد"""
        try:
            self.api_key = api_key
            self.profile_key = profile_key or self.profile_key
            self.headers['Authorization'] = f'Bearer {api_key}'

            # حفظ في الإعدادات
            config.set('api_key', api_key)
            if profile_key:
                config.set('profile_key', profile_key)

            logger.info("تم تعيين بيانات الاعتماد بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في تعيين بيانات الاعتماد: {e}")
            return False

    def test_connection(self) -> tuple[bool, str]:
        """اختبار الاتصال مع API"""
        try:
            if not self.api_key:
                return False, "لم يتم تعيين API Key"

            if not self.api_key.strip():
                return False, "API Key فارغ"

            # اختبار بسيط للحصول على الملف الشخصي
            url = f"{self.base_url}/user"
            response = requests.get(url, headers=self.headers, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    logger.info("تم اختبار الاتصال بنجاح")
                    return True, "الاتصال ناجح"

                # معالجة رسائل الخطأ من Ayrshare
                original_msg = result.get('message', 'خطأ غير معروف')
                logger.error(f"فشل اختبار الاتصال: {original_msg}")

                # تحويل الرسائل الإنجليزية إلى العربية
                if "API key not valid" in original_msg or "not valid" in original_msg:
                    return False, "API Key غير صالح"
                elif "unauthorized" in original_msg.lower() or "forbidden" in original_msg.lower():
                    return False, "API Key غير مصرح"
                elif "rate limit" in original_msg.lower() or "too many" in original_msg.lower():
                    return False, "تجاوز حدود الطلبات"
                else:
                    return False, "خطأ في API Key أو الإعدادات"
            elif response.status_code == 403:
                logger.error("API Key غير صالح - HTTP 403")
                return False, "API Key غير صالح"
            elif response.status_code == 401:
                logger.error("API Key غير مصرح - HTTP 401")
                return False, "API Key غير مصرح"
            elif response.status_code == 404:
                logger.error("الخدمة غير متاحة - HTTP 404")
                return False, "الخدمة غير متاحة"
            elif response.status_code == 429:
                logger.error("تجاوز حدود الطلبات - HTTP 429")
                return False, "تجاوز حدود الطلبات"
            elif response.status_code >= 500:
                logger.error(f"خطأ في الخادم - HTTP {response.status_code}")
                return False, "خطأ في خادم Ayrshare"
            else:
                logger.error(f"خطأ غير معروف - HTTP {response.status_code}")
                return False, f"خطأ غير معروف (كود: {response.status_code})"

        except requests.exceptions.ConnectionError as e:
            if "Name or service not known" in str(e) or "getaddrinfo failed" in str(e):
                error_msg = "لا يوجد اتصال بالإنترنت"
            elif "Connection refused" in str(e):
                error_msg = "تم رفض الاتصال من الخادم"
            else:
                error_msg = "مشكلة في الاتصال بالشبكة"
            logger.error(f"خطأ في الاتصال: {e}")
            return False, error_msg
        except requests.exceptions.Timeout:
            error_msg = "انتهت مهلة الاتصال - الشبكة بطيئة"
            logger.error(f"خطأ في الاتصال: انتهت المهلة")
            return False, error_msg
        except requests.exceptions.SSLError:
            error_msg = "خطأ في شهادة الأمان"
            logger.error(f"خطأ SSL في الاتصال")
            return False, error_msg
        except requests.exceptions.RequestException as e:
            error_msg = "خطأ في طلب الشبكة"
            logger.error(f"خطأ في اختبار الاتصال: {e}")
            return False, error_msg
        except Exception as e:
            error_msg = "خطأ غير متوقع في النظام"
            logger.error(f"خطأ في اختبار الاتصال: {e}")
            return False, error_msg

    def post_content(self, content: str, platforms: List[str],
                    media_urls: Optional[List[str]] = None,
                    hashtags: Optional[str] = None,
                    scheduled_time: Optional[datetime] = None) -> Dict[str, Any]:
        """نشر محتوى على المنصات المحددة"""
        try:
            if not self.api_key:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            # إعداد البيانات للنشر
            post_data = {
                'post': content,
                'platforms': platforms
            }

            # إضافة الملف الشخصي إذا كان متوفراً
            if self.profile_key:
                post_data['profileKey'] = self.profile_key

            # إضافة الوسائط
            if media_urls:
                post_data['mediaUrls'] = media_urls

            # إضافة الهاشتاجات للمحتوى
            if hashtags:
                post_data['post'] = f"{content} {hashtags}"

            # إضافة الجدولة
            if scheduled_time:
                post_data['scheduleDate'] = scheduled_time.isoformat()

            # إرسال الطلب
            start_time = time.time()
            url = f"{self.base_url}/post"
            response = requests.post(url, json=post_data, headers=self.headers, timeout=30)
            response_time = time.time() - start_time

            if response.status_code == 200:
                result = response.json()

                # إزالة رسالة "[Sent with Free Plan]" من النتيجة إذا كانت موجودة
                if isinstance(result, dict):
                    # تنظيف الرسائل من النتيجة
                    for key, value in result.items():
                        if isinstance(value, str) and "[Sent with Free Plan]" in value:
                            result[key] = value.replace("[Sent with Free Plan]", "").strip()
                        elif isinstance(value, dict):
                            for sub_key, sub_value in value.items():
                                if isinstance(sub_value, str) and "[Sent with Free Plan]" in sub_value:
                                    value[sub_key] = sub_value.replace("[Sent with Free Plan]", "").strip()
                        elif isinstance(value, list):
                            for i, item in enumerate(value):
                                if isinstance(item, str) and "[Sent with Free Plan]" in item:
                                    value[i] = item.replace("[Sent with Free Plan]", "").strip()
                                elif isinstance(item, dict):
                                    for sub_key, sub_value in item.items():
                                        if isinstance(sub_value, str) and "[Sent with Free Plan]" in sub_value:
                                            item[sub_key] = sub_value.replace("[Sent with Free Plan]", "").strip()
            else:
                result = {
                    'status': 'error',
                    'message': f'HTTP {response.status_code}: {response.text[:100]}'
                }

            # إضافة وقت الاستجابة
            result['response_time'] = response_time

            if result.get('status') == 'success':
                logger.info(f"تم النشر بنجاح على {platforms}")
                return result

            error_msg = result.get('message', 'خطأ غير معروف')
            logger.error(f"فشل النشر: {error_msg}")
            return result

        except Exception as e:
            logger.error(f"خطأ في النشر: {e}")
            return {'status': 'error', 'message': f'خطأ في النشر: {e}'}

    def upload_media(self, file_path: str) -> Dict[str, Any]:
        """رفع ملف وسائط"""
        try:
            if not self.api_key:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            # التحقق من حجم الملف
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)

            # تحديد timeout حسب حجم الملف
            if file_size_mb < 10:  # أقل من 10 ميجا
                timeout = 120  # دقيقتان
            elif file_size_mb < 50:  # أقل من 50 ميجا
                timeout = 300  # 5 دقائق
            elif file_size_mb < 100:  # أقل من 100 ميجا
                timeout = 600  # 10 دقائق
            else:  # أكبر من 100 ميجا
                timeout = 1200  # 20 دقيقة

            logger.info(f"رفع ملف: {os.path.basename(file_path)} ({file_size_mb:.1f} MB) - timeout: {timeout}s")

            # إعداد الملف للرفع
            with open(file_path, 'rb') as file:
                files = {'file': file}
                headers = {'Authorization': f'Bearer {self.api_key}'}

                url = f"{self.base_url}/upload"

                # إعدادات محسنة للرفع
                response = requests.post(
                    url,
                    files=files,
                    headers=headers,
                    timeout=timeout,
                    stream=True  # للملفات الكبيرة
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get('status') == 'success':
                        logger.info(f"تم رفع الملف بنجاح: {file_path}")
                        return result

                    error_msg = result.get('message', 'خطأ في رفع الملف')
                    logger.error(f"فشل رفع الملف: {error_msg}")
                    return result

                result = {
                    'status': 'error',
                    'message': f'HTTP {response.status_code}: {response.text[:100]}'
                }
                logger.error(f"فشل رفع الملف: {result['message']}")
                return result

        except requests.exceptions.Timeout:
            error_msg = f"انتهت مهلة رفع الملف ({timeout}s) - الملف كبير أو الشبكة بطيئة"
            logger.error(f"خطأ timeout في رفع الملف: {file_path}")
            return {'status': 'error', 'message': error_msg}
        except requests.exceptions.ConnectionError as e:
            if "Connection aborted" in str(e):
                error_msg = "انقطع الاتصال أثناء رفع الملف - جرب مرة أخرى"
            else:
                error_msg = "مشكلة في الاتصال أثناء رفع الملف"
            logger.error(f"خطأ اتصال في رفع الملف: {e}")
            return {'status': 'error', 'message': error_msg}
        except FileNotFoundError:
            error_msg = "الملف غير موجود"
            logger.error(f"الملف غير موجود: {file_path}")
            return {'status': 'error', 'message': error_msg}
        except PermissionError:
            error_msg = "لا يوجد صلاحية لقراءة الملف"
            logger.error(f"لا يوجد صلاحية للملف: {file_path}")
            return {'status': 'error', 'message': error_msg}
        except Exception as e:
            error_msg = f"خطأ في رفع الملف: {str(e)}"
            logger.error(f"خطأ عام في رفع الملف: {e}")
            return {'status': 'error', 'message': error_msg}

    def get_post_history(self, limit: int = 50) -> Dict[str, Any]:
        """الحصول على تاريخ المنشورات"""
        try:
            if not self.api_key:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            params = {'last': limit}
            if self.profile_key:
                params['profileKey'] = self.profile_key

            url = f"{self.base_url}/history"
            response = requests.get(url, params=params, headers=self.headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    logger.info(f"تم الحصول على تاريخ المنشورات: {limit} منشور")
                    return result

                error_msg = result.get('message', 'خطأ في الحصول على التاريخ')
                logger.error(f"فشل الحصول على التاريخ: {error_msg}")
                return result

            result = {
                'status': 'error',
                'message': f'HTTP {response.status_code}: {response.text[:100]}'
            }
            logger.error(f"فشل الحصول على التاريخ: {result['message']}")
            return result

        except Exception as e:
            logger.error(f"خطأ في الحصول على التاريخ: {e}")
            return {'status': 'error', 'message': f'خطأ في الحصول على التاريخ: {e}'}

    def delete_post(self, post_id: str) -> Dict[str, Any]:
        """حذف منشور"""
        try:
            if not self.api_key:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            delete_data = {'id': post_id}
            if self.profile_key:
                delete_data['profileKey'] = self.profile_key

            url = f"{self.base_url}/delete"
            response = requests.delete(url, json=delete_data, headers=self.headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    logger.info(f"تم حذف المنشور: {post_id}")
                    return result

                error_msg = result.get('message', 'خطأ في حذف المنشور')
                logger.error(f"فشل حذف المنشور: {error_msg}")
                return result

            result = {
                'status': 'error',
                'message': f'HTTP {response.status_code}: {response.text[:100]}'
            }
            logger.error(f"فشل حذف المنشور: {result['message']}")
            return result

        except Exception as e:
            logger.error(f"خطأ في حذف المنشور: {e}")
            return {'status': 'error', 'message': f'خطأ في حذف المنشور: {e}'}

# إنشاء مثيل عام للعميل
ayrshare_client = SimpleAyrshareClient()
