# -*- coding: utf-8 -*-
"""
عميل Ayrshare API مبسط
"""
import time
import requests
from typing import Dict, List, Optional, Any
from datetime import datetime
from utils.logger import logger
from utils.config import config

class SimpleAyrshareClient:
    """عميل مبسط للتعامل مع Ayrshare API"""

    def __init__(self, api_key: Optional[str] = None, profile_key: Optional[str] = None):
        self.api_key = api_key or config.get('api_key', '')
        self.profile_key = profile_key or config.get('profile_key', '')
        self.base_url = "https://api.ayrshare.com/api"
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}' if self.api_key else ''
        }

    def set_credentials(self, api_key: str, profile_key: Optional[str] = None) -> bool:
        """تعيين بيانات الاعتماد"""
        try:
            self.api_key = api_key
            self.profile_key = profile_key or self.profile_key
            self.headers['Authorization'] = f'Bearer {api_key}'

            # حفظ في الإعدادات
            config.set('api_key', api_key)
            if profile_key:
                config.set('profile_key', profile_key)

            logger.info("تم تعيين بيانات الاعتماد بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في تعيين بيانات الاعتماد: {e}")
            return False

    def test_connection(self) -> tuple[bool, str]:
        """اختبار الاتصال مع API"""
        try:
            if not self.api_key:
                return False, "لم يتم تعيين API Key"

            # اختبار بسيط للحصول على الملف الشخصي
            url = f"{self.base_url}/user"
            response = requests.get(url, headers=self.headers, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    logger.info("تم اختبار الاتصال بنجاح")
                    return True, "الاتصال ناجح"

                error_msg = result.get('message', 'خطأ غير معروف')
                logger.error(f"فشل اختبار الاتصال: {error_msg}")
                return False, error_msg

            error_msg = f"HTTP {response.status_code}: {response.text}"
            logger.error(f"فشل اختبار الاتصال: {error_msg}")
            return False, error_msg

        except Exception as e:
            logger.error(f"خطأ في اختبار الاتصال: {e}")
            return False, f"خطأ في الاتصال: {e}"

    def post_content(self, content: str, platforms: List[str],
                    media_urls: Optional[List[str]] = None,
                    hashtags: Optional[str] = None,
                    scheduled_time: Optional[datetime] = None) -> Dict[str, Any]:
        """نشر محتوى على المنصات المحددة"""
        try:
            if not self.api_key:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            # إعداد البيانات للنشر
            post_data = {
                'post': content,
                'platforms': platforms
            }

            # إضافة الملف الشخصي إذا كان متوفراً
            if self.profile_key:
                post_data['profileKey'] = self.profile_key

            # إضافة الوسائط
            if media_urls:
                post_data['mediaUrls'] = media_urls

            # إضافة الهاشتاجات للمحتوى
            if hashtags:
                post_data['post'] = f"{content} {hashtags}"

            # إضافة الجدولة
            if scheduled_time:
                post_data['scheduleDate'] = scheduled_time.isoformat()

            # إرسال الطلب
            start_time = time.time()
            url = f"{self.base_url}/post"
            response = requests.post(url, json=post_data, headers=self.headers, timeout=30)
            response_time = time.time() - start_time

            if response.status_code == 200:
                result = response.json()
            else:
                result = {
                    'status': 'error',
                    'message': f'HTTP {response.status_code}: {response.text[:100]}'
                }

            # إضافة وقت الاستجابة
            result['response_time'] = response_time

            if result.get('status') == 'success':
                logger.info(f"تم النشر بنجاح على {platforms}")
                return result

            error_msg = result.get('message', 'خطأ غير معروف')
            logger.error(f"فشل النشر: {error_msg}")
            return result

        except Exception as e:
            logger.error(f"خطأ في النشر: {e}")
            return {'status': 'error', 'message': f'خطأ في النشر: {e}'}

    def upload_media(self, file_path: str) -> Dict[str, Any]:
        """رفع ملف وسائط"""
        try:
            if not self.api_key:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            # إعداد الملف للرفع
            with open(file_path, 'rb') as file:
                files = {'file': file}
                headers = {'Authorization': f'Bearer {self.api_key}'}

                url = f"{self.base_url}/upload"
                response = requests.post(url, files=files, headers=headers, timeout=60)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('status') == 'success':
                        logger.info(f"تم رفع الملف بنجاح: {file_path}")
                        return result

                    error_msg = result.get('message', 'خطأ في رفع الملف')
                    logger.error(f"فشل رفع الملف: {error_msg}")
                    return result

                result = {
                    'status': 'error',
                    'message': f'HTTP {response.status_code}: {response.text[:100]}'
                }
                logger.error(f"فشل رفع الملف: {result['message']}")
                return result

        except Exception as e:
            logger.error(f"خطأ في رفع الملف: {e}")
            return {'status': 'error', 'message': f'خطأ في رفع الملف: {e}'}

    def get_post_history(self, limit: int = 50) -> Dict[str, Any]:
        """الحصول على تاريخ المنشورات"""
        try:
            if not self.api_key:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            params = {'last': limit}
            if self.profile_key:
                params['profileKey'] = self.profile_key

            url = f"{self.base_url}/history"
            response = requests.get(url, params=params, headers=self.headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    logger.info(f"تم الحصول على تاريخ المنشورات: {limit} منشور")
                    return result

                error_msg = result.get('message', 'خطأ في الحصول على التاريخ')
                logger.error(f"فشل الحصول على التاريخ: {error_msg}")
                return result

            result = {
                'status': 'error',
                'message': f'HTTP {response.status_code}: {response.text[:100]}'
            }
            logger.error(f"فشل الحصول على التاريخ: {result['message']}")
            return result

        except Exception as e:
            logger.error(f"خطأ في الحصول على التاريخ: {e}")
            return {'status': 'error', 'message': f'خطأ في الحصول على التاريخ: {e}'}

    def delete_post(self, post_id: str) -> Dict[str, Any]:
        """حذف منشور"""
        try:
            if not self.api_key:
                return {'status': 'error', 'message': 'لم يتم تعيين API Key'}

            delete_data = {'id': post_id}
            if self.profile_key:
                delete_data['profileKey'] = self.profile_key

            url = f"{self.base_url}/delete"
            response = requests.delete(url, json=delete_data, headers=self.headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    logger.info(f"تم حذف المنشور: {post_id}")
                    return result

                error_msg = result.get('message', 'خطأ في حذف المنشور')
                logger.error(f"فشل حذف المنشور: {error_msg}")
                return result

            result = {
                'status': 'error',
                'message': f'HTTP {response.status_code}: {response.text[:100]}'
            }
            logger.error(f"فشل حذف المنشور: {result['message']}")
            return result

        except Exception as e:
            logger.error(f"خطأ في حذف المنشور: {e}")
            return {'status': 'error', 'message': f'خطأ في حذف المنشور: {e}'}

# إنشاء مثيل عام للعميل
ayrshare_client = SimpleAyrshareClient()
