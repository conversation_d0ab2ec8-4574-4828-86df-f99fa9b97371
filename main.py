# -*- coding: utf-8 -*-
"""
Ayrshare Social Media Manager
برنامج إدارة وسائل التواصل الاجتماعي باستخدام Ayrshare API

الميزات:
- نشر المحتوى على Facebook, Instagram, TikTok
- جدولة المنشورات
- تخطي حدود المنصات
- إدارة الوسائط (صور وفيديوهات)
- واجهة رسومية سهلة الاستخدام
- حفظ الإعدادات تلقائياً
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import threading
import time
from datetime import datetime

# إضافة المجلد الحالي لمسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # استيراد المكونات الأساسية
    from utils.logger import logger
    from utils.config import config
    from database.database import db
    from gui.main_window import MainWindow
    from api.ayrshare_client import ayrshare_client
    from api.rate_limiter import rate_limiter
    
    logger.info("تم تحميل جميع المكونات بنجاح")
    
except ImportError as e:
    print(f"خطأ في استيراد المكونات: {e}")
    print("يرجى التأكد من تثبيت جميع المتطلبات باستخدام: pip install -r requirements.txt")
    sys.exit(1)

class SchedulerThread:
    """خيط منفصل لإدارة الجدولة"""
    
    def __init__(self):
        self.running = False
        self.thread = None
    
    def start(self):
        """بدء خيط الجدولة"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.thread.start()
            logger.info("تم بدء خيط الجدولة")
    
    def stop(self):
        """إيقاف خيط الجدولة"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        logger.info("تم إيقاف خيط الجدولة")
    
    def _run_scheduler(self):
        """تشغيل مجدول المنشورات"""
        while self.running:
            try:
                # فحص المنشورات المستحقة للنشر
                due_posts = db.get_due_posts()
                
                for scheduled_post in due_posts:
                    try:
                        # الحصول على بيانات المنشور
                        post = db.get_post(scheduled_post.post_id)
                        if not post:
                            continue
                        
                        logger.info(f"نشر منشور مجدول: {post.title}")
                        
                        # رفع الملفات إذا كانت موجودة
                        media_urls = []
                        if post.media_paths:
                            for file_path in post.media_paths:
                                if os.path.exists(file_path):
                                    upload_result = ayrshare_client.upload_media(file_path)
                                    if upload_result.get('status') == 'success':
                                        media_urls.append(upload_result.get('url'))
                        
                        # نشر المحتوى
                        result = ayrshare_client.post_content(
                            content=post.content,
                            platforms=post.platforms,
                            media_urls=media_urls if media_urls else None,
                            hashtags=post.hashtags if post.hashtags else None
                        )
                        
                        if result.get('status') == 'success':
                            # تحديث حالة المنشور
                            post.status = "published"
                            post.published_at = datetime.now()
                            post.ayrshare_id = result.get('id')
                            db.update_post(post)
                            
                            # تعطيل الجدولة
                            scheduled_post.is_active = False
                            # هنا يمكن إضافة تحديث الجدولة في قاعدة البيانات
                            
                            logger.info(f"تم نشر المنشور المجدول بنجاح: {post.title}")
                            
                        else:
                            # تسجيل الخطأ
                            error_msg = result.get('message', 'خطأ غير معروف')
                            post.error_message = error_msg
                            db.update_post(post)
                            
                            logger.error(f"فشل نشر المنشور المجدول: {error_msg}")
                    
                    except Exception as e:
                        logger.error(f"خطأ في نشر منشور مجدول: {e}")
                
                # انتظار دقيقة واحدة قبل الفحص التالي
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"خطأ في مجدول المنشورات: {e}")
                time.sleep(60)

def check_requirements():
    """فحص المتطلبات الأساسية"""
    try:
        # فحص المكتبات المطلوبة
        required_modules = [
            'customtkinter',
            'requests',
            'ayrshare',
            'PIL'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            error_msg = f"المكتبات التالية مفقودة: {', '.join(missing_modules)}\n"
            error_msg += "يرجى تثبيتها باستخدام: pip install -r requirements.txt"
            messagebox.showerror("مكتبات مفقودة", error_msg)
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"خطأ في فحص المتطلبات: {e}")
        return False

def setup_directories():
    """إنشاء المجلدات المطلوبة"""
    try:
        directories = [
            'logs',
            'uploads',
            'thumbnails',
            'exports'
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"تم إنشاء مجلد: {directory}")
        
        return True
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء المجلدات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        logger.info("بدء تشغيل Ayrshare Social Media Manager")
        
        # فحص المتطلبات
        if not check_requirements():
            return
        
        # إنشاء المجلدات المطلوبة
        if not setup_directories():
            logger.error("فشل في إنشاء المجلدات المطلوبة")
            return
        
        # تهيئة قاعدة البيانات
        logger.info("تهيئة قاعدة البيانات...")
        db.init_database()
        
        # بدء مجدول المنشورات
        scheduler = SchedulerThread()
        scheduler.start()
        
        # إنشاء وتشغيل الواجهة الرسومية
        logger.info("بدء تشغيل الواجهة الرسومية...")
        app = MainWindow()
        
        try:
            app.run()
        finally:
            # إيقاف المجدول عند إغلاق التطبيق
            scheduler.stop()
            logger.info("تم إغلاق التطبيق")
    
    except KeyboardInterrupt:
        logger.info("تم إيقاف التطبيق بواسطة المستخدم")
    
    except Exception as e:
        logger.error(f"خطأ في التطبيق الرئيسي: {e}")
        messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    main()
