# -*- coding: utf-8 -*-
"""
نظام الإشعارات
"""
import tkinter as tk
from tkinter import messagebox
import threading
import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from utils.logger import logger
from utils.config import config

class NotificationManager:
    """مدير الإشعارات"""
    
    def __init__(self):
        self.notifications = []
        self.notification_window = None
        self.enabled = config.get('notifications.enabled', True)
        self.sound_enabled = config.get('notifications.sound_enabled', True)
        self.desktop_enabled = config.get('notifications.desktop_enabled', True)
        
    def show_success(self, title: str, message: str, duration: int = 3000):
        """عرض إشعار نجاح"""
        if not self.enabled:
            return
            
        try:
            if self.desktop_enabled:
                self._show_desktop_notification(title, message, "success", duration)
            
            logger.info(f"إشعار نجاح: {title} - {message}")
            
        except Exception as e:
            logger.error(f"خطأ في عرض إشعار النجاح: {e}")
    
    def show_error(self, title: str, message: str, duration: int = 5000):
        """عرض إشعار خطأ"""
        if not self.enabled:
            return
            
        try:
            if self.desktop_enabled:
                self._show_desktop_notification(title, message, "error", duration)
            
            logger.error(f"إشعار خطأ: {title} - {message}")
            
        except Exception as e:
            logger.error(f"خطأ في عرض إشعار الخطأ: {e}")
    
    def show_warning(self, title: str, message: str, duration: int = 4000):
        """عرض إشعار تحذير"""
        if not self.enabled:
            return
            
        try:
            if self.desktop_enabled:
                self._show_desktop_notification(title, message, "warning", duration)
            
            logger.warning(f"إشعار تحذير: {title} - {message}")
            
        except Exception as e:
            logger.error(f"خطأ في عرض إشعار التحذير: {e}")
    
    def show_info(self, title: str, message: str, duration: int = 3000):
        """عرض إشعار معلومات"""
        if not self.enabled:
            return
            
        try:
            if self.desktop_enabled:
                self._show_desktop_notification(title, message, "info", duration)
            
            logger.info(f"إشعار معلومات: {title} - {message}")
            
        except Exception as e:
            logger.error(f"خطأ في عرض إشعار المعلومات: {e}")
    
    def _show_desktop_notification(self, title: str, message: str, 
                                 notification_type: str, duration: int):
        """عرض إشعار على سطح المكتب"""
        try:
            # إنشاء نافذة إشعار مخصصة
            notification = NotificationWindow(title, message, notification_type, duration)
            notification.show()
            
        except Exception as e:
            logger.error(f"خطأ في عرض إشعار سطح المكتب: {e}")
    
    def show_scheduled_post_reminder(self, post_title: str, scheduled_time: datetime):
        """إشعار تذكير بمنشور مجدول"""
        time_str = scheduled_time.strftime("%H:%M")
        message = f"سيتم نشر '{post_title}' في {time_str}"
        self.show_info("تذكير جدولة", message)
    
    def show_rate_limit_warning(self, platform: str, limit_type: str):
        """إشعار تحذير حد المعدل"""
        message = f"اقتربت من الحد {limit_type} لمنصة {platform}"
        self.show_warning("تحذير حد المعدل", message)
    
    def show_api_error(self, error_message: str):
        """إشعار خطأ API"""
        self.show_error("خطأ API", f"خطأ في الاتصال: {error_message}")
    
    def show_post_success(self, platforms: List[str], post_id: Optional[str] = None):
        """إشعار نجاح النشر"""
        platforms_str = ", ".join(platforms)
        message = f"تم النشر بنجاح على: {platforms_str}"
        if post_id:
            message += f"\nمعرف المنشور: {post_id}"
        self.show_success("نشر ناجح", message)
    
    def show_upload_progress(self, filename: str, progress: int):
        """إشعار تقدم الرفع"""
        if progress == 100:
            self.show_success("رفع مكتمل", f"تم رفع {filename} بنجاح")
        elif progress % 25 == 0:  # إشعار كل 25%
            self.show_info("جاري الرفع", f"رفع {filename}: {progress}%")
    
    def enable_notifications(self, enabled: bool = True):
        """تفعيل أو إلغاء الإشعارات"""
        self.enabled = enabled
        config.set('notifications.enabled', enabled)
        logger.info(f"الإشعارات: {'مفعلة' if enabled else 'معطلة'}")
    
    def enable_sound(self, enabled: bool = True):
        """تفعيل أو إلغاء أصوات الإشعارات"""
        self.sound_enabled = enabled
        config.set('notifications.sound_enabled', enabled)
    
    def enable_desktop_notifications(self, enabled: bool = True):
        """تفعيل أو إلغاء إشعارات سطح المكتب"""
        self.desktop_enabled = enabled
        config.set('notifications.desktop_enabled', enabled)

class NotificationWindow:
    """نافذة إشعار مخصصة"""
    
    def __init__(self, title: str, message: str, notification_type: str, duration: int):
        self.title = title
        self.message = message
        self.notification_type = notification_type
        self.duration = duration
        self.window = None
        
        # ألوان الإشعارات
        self.colors = {
            'success': {'bg': '#4CAF50', 'fg': 'white'},
            'error': {'bg': '#F44336', 'fg': 'white'},
            'warning': {'bg': '#FF9800', 'fg': 'white'},
            'info': {'bg': '#2196F3', 'fg': 'white'}
        }
    
    def show(self):
        """عرض نافذة الإشعار"""
        try:
            self.window = tk.Toplevel()
            self.window.title(self.title)
            self.window.geometry("300x100")
            self.window.resizable(False, False)
            
            # إعداد النافذة
            self.window.attributes('-topmost', True)
            self.window.overrideredirect(True)  # إزالة شريط العنوان
            
            # موضع النافذة (أعلى يمين الشاشة)
            screen_width = self.window.winfo_screenwidth()
            x = screen_width - 320
            y = 50
            self.window.geometry(f"300x100+{x}+{y}")
            
            # ألوان الإشعار
            colors = self.colors.get(self.notification_type, self.colors['info'])
            self.window.configure(bg=colors['bg'])
            
            # إطار المحتوى
            content_frame = tk.Frame(self.window, bg=colors['bg'])
            content_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # عنوان الإشعار
            title_label = tk.Label(
                content_frame,
                text=self.title,
                font=('Arial', 12, 'bold'),
                bg=colors['bg'],
                fg=colors['fg'],
                wraplength=280
            )
            title_label.pack(anchor='w')
            
            # رسالة الإشعار
            message_label = tk.Label(
                content_frame,
                text=self.message,
                font=('Arial', 10),
                bg=colors['bg'],
                fg=colors['fg'],
                wraplength=280,
                justify='right'
            )
            message_label.pack(anchor='w', pady=(5, 0))
            
            # زر الإغلاق
            close_button = tk.Button(
                content_frame,
                text="×",
                font=('Arial', 12, 'bold'),
                bg=colors['bg'],
                fg=colors['fg'],
                border=0,
                command=self.close
            )
            close_button.place(relx=1.0, rely=0.0, anchor='ne')
            
            # إغلاق تلقائي
            self.window.after(self.duration, self.close)
            
            # تأثير الظهور
            self._animate_show()
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء نافذة الإشعار: {e}")
    
    def close(self):
        """إغلاق نافذة الإشعار"""
        try:
            if self.window:
                self._animate_hide()
        except Exception as e:
            logger.error(f"خطأ في إغلاق نافذة الإشعار: {e}")
    
    def _animate_show(self):
        """تأثير ظهور النافذة"""
        try:
            # تأثير انزلاق من اليمين
            screen_width = self.window.winfo_screenwidth()
            start_x = screen_width
            end_x = screen_width - 320
            
            def slide_in(step=0):
                if step <= 10:
                    current_x = start_x - (start_x - end_x) * (step / 10)
                    self.window.geometry(f"300x100+{int(current_x)}+50")
                    self.window.after(20, lambda: slide_in(step + 1))
            
            slide_in()
            
        except Exception as e:
            logger.error(f"خطأ في تأثير الظهور: {e}")
    
    def _animate_hide(self):
        """تأثير إخفاء النافذة"""
        try:
            # تأثير انزلاق إلى اليمين
            screen_width = self.window.winfo_screenwidth()
            start_x = screen_width - 320
            end_x = screen_width
            
            def slide_out(step=0):
                if step <= 10:
                    current_x = start_x + (end_x - start_x) * (step / 10)
                    self.window.geometry(f"300x100+{int(current_x)}+50")
                    if step == 10:
                        self.window.destroy()
                    else:
                        self.window.after(20, lambda: slide_out(step + 1))
            
            slide_out()
            
        except Exception as e:
            logger.error(f"خطأ في تأثير الإخفاء: {e}")
            if self.window:
                self.window.destroy()

# إنشاء مثيل عام لمدير الإشعارات
notification_manager = NotificationManager()
