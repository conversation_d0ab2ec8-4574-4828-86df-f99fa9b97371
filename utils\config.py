# -*- coding: utf-8 -*-
"""
إدارة الإعدادات والتكوين
"""
import json
import os
from typing import Dict, Any, Optional

class ConfigManager:
    """مدير الإعدادات لحفظ وتحميل إعدادات التطبيق"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.default_config = {
            "api_key": "",
            "profile_key": "",
            "default_platforms": ["facebook", "instagram", "tiktok"],
            "auto_save": True,
            "rate_limits": {
                "facebook": {
                    "posts_per_hour": 25,
                    "reels_per_day": 30
                },
                "instagram": {
                    "posts_per_hour": 25,
                    "stories_per_day": 100
                },
                "tiktok": {
                    "posts_per_hour": 10,
                    "videos_per_day": 50
                }
            },
            "default_hashtags": {
                "facebook": "#facebook #social #content",
                "instagram": "#instagram #insta #photo #video",
                "tiktok": "#tiktok #viral #trending #fyp"
            },
            "media_settings": {
                "max_file_size_mb": 100,
                "supported_video_formats": [".mp4", ".mov", ".avi", ".mkv"],
                "supported_image_formats": [".jpg", ".jpeg", ".png", ".gif"]
            },
            "ui_settings": {
                "theme": "dark",
                "language": "ar",
                "window_size": "1200x800"
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # دمج الإعدادات الافتراضية مع المحفوظة
                return self._merge_configs(self.default_config, config)
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            return self.default_config.copy()
    
    def save_config(self) -> bool:
        """حفظ الإعدادات في الملف"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """الحصول على قيمة إعداد"""
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def set(self, key: str, value: Any) -> None:
        """تعيين قيمة إعداد"""
        keys = key.split('.')
        config = self.config
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        config[keys[-1]] = value
        
        if self.get('auto_save', True):
            self.save_config()
    
    def _merge_configs(self, default: Dict, user: Dict) -> Dict:
        """دمج الإعدادات الافتراضية مع إعدادات المستخدم"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def reset_to_default(self) -> None:
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        self.config = self.default_config.copy()
        self.save_config()
    
    def export_config(self, file_path: str) -> bool:
        """تصدير الإعدادات لملف"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"خطأ في تصدير الإعدادات: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """استيراد الإعدادات من ملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            self.config = self._merge_configs(self.default_config, imported_config)
            self.save_config()
            return True
        except Exception as e:
            print(f"خطأ في استيراد الإعدادات: {e}")
            return False

# إنشاء مثيل عام للإعدادات
config = ConfigManager()
