#!/bin/bash

# Ayrshare Social Media Manager
# Script for Linux/macOS

echo "========================================"
echo "   Ayrshare Social Media Manager"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "خطأ: Python3 غير مثبت"
    echo "يرجى تثبيت Python 3.10 أو أحدث"
    exit 1
fi

echo "تم العثور على Python..."

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "خطأ: pip3 غير متوفر"
    echo "يرجى تثبيت pip3"
    exit 1
fi

echo "تم العثور على pip..."

# Check if requirements.txt exists
if [ ! -f "requirements.txt" ]; then
    echo "خطأ: ملف requirements.txt غير موجود"
    exit 1
fi

echo "جاري فحص المتطلبات..."

# Install requirements
pip3 install -r requirements.txt --quiet

if [ $? -ne 0 ]; then
    echo "خطأ في تثبيت المتطلبات"
    echo "جاري المحاولة مرة أخرى..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "فشل في تثبيت المتطلبات"
        echo "يرجى تشغيل الأمر التالي يدوياً:"
        echo "pip3 install -r requirements.txt"
        exit 1
    fi
fi

echo "تم تثبيت المتطلبات بنجاح..."

# Check if main.py exists
if [ ! -f "main.py" ]; then
    echo "خطأ: ملف main.py غير موجود"
    exit 1
fi

echo
echo "جاري تشغيل البرنامج..."
echo

# Run the program
python3 main.py

# Check exit status
if [ $? -ne 0 ]; then
    echo
    echo "حدث خطأ أثناء تشغيل البرنامج"
    echo "يرجى مراجعة ملفات السجل في مجلد logs للمزيد من التفاصيل"
    echo
fi

echo
echo "تم إغلاق البرنامج"
read -p "اضغط Enter للمتابعة..."
