# -*- coding: utf-8 -*-
"""
نظام الإحصائيات والتحليلات
"""
import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, Counter
from utils.logger import logger
from utils.config import config

class AnalyticsManager:
    """مدير الإحصائيات والتحليلات"""
    
    def __init__(self, db_path: str = "analytics.db"):
        self.db_path = db_path
        self.init_analytics_db()
    
    def init_analytics_db(self):
        """إنشاء قاعدة بيانات الإحصائيات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول إحصائيات النشر
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS post_analytics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        post_id TEXT,
                        platform TEXT,
                        post_type TEXT,
                        content_length INTEGER,
                        media_count INTEGER,
                        hashtags_count INTEGER,
                        posted_at TEXT,
                        success BOOLEAN,
                        error_message TEXT,
                        response_time REAL
                    )
                ''')
                
                # جدول إحصائيات الاستخدام اليومي
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS daily_usage (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date TEXT UNIQUE,
                        total_posts INTEGER DEFAULT 0,
                        successful_posts INTEGER DEFAULT 0,
                        failed_posts INTEGER DEFAULT 0,
                        facebook_posts INTEGER DEFAULT 0,
                        instagram_posts INTEGER DEFAULT 0,
                        tiktok_posts INTEGER DEFAULT 0,
                        total_media_uploaded INTEGER DEFAULT 0,
                        total_response_time REAL DEFAULT 0
                    )
                ''')
                
                # جدول إحصائيات الأداء
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        metric_name TEXT,
                        metric_value REAL,
                        recorded_at TEXT
                    )
                ''')
                
                conn.commit()
                logger.info("تم إنشاء قاعدة بيانات الإحصائيات")
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء قاعدة بيانات الإحصائيات: {e}")
    
    def record_post_attempt(self, post_id: str, platform: str, post_type: str,
                          content_length: int, media_count: int, hashtags_count: int,
                          success: bool, error_message: Optional[str] = None,
                          response_time: Optional[float] = None):
        """تسجيل محاولة نشر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO post_analytics 
                    (post_id, platform, post_type, content_length, media_count, 
                     hashtags_count, posted_at, success, error_message, response_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    post_id, platform, post_type, content_length, media_count,
                    hashtags_count, datetime.now().isoformat(), success,
                    error_message, response_time
                ))
                
                conn.commit()
                
                # تحديث الإحصائيات اليومية
                self._update_daily_usage(platform, success, media_count, response_time)
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل محاولة النشر: {e}")
    
    def _update_daily_usage(self, platform: str, success: bool, 
                          media_count: int, response_time: Optional[float]):
        """تحديث إحصائيات الاستخدام اليومي"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # إنشاء سجل اليوم إذا لم يكن موجوداً
                cursor.execute('''
                    INSERT OR IGNORE INTO daily_usage (date) VALUES (?)
                ''', (today,))
                
                # تحديث الإحصائيات
                platform_column = f"{platform}_posts"
                
                update_query = f'''
                    UPDATE daily_usage SET
                        total_posts = total_posts + 1,
                        {platform_column} = {platform_column} + 1,
                        total_media_uploaded = total_media_uploaded + ?,
                        total_response_time = total_response_time + ?
                '''
                
                if success:
                    update_query += ', successful_posts = successful_posts + 1'
                else:
                    update_query += ', failed_posts = failed_posts + 1'
                
                update_query += ' WHERE date = ?'
                
                cursor.execute(update_query, (
                    media_count, response_time or 0, today
                ))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"خطأ في تحديث الإحصائيات اليومية: {e}")
    
    def get_daily_stats(self, days: int = 7) -> List[Dict]:
        """الحصول على إحصائيات الأيام الماضية"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM daily_usage 
                    WHERE date >= ? AND date <= ?
                    ORDER BY date DESC
                ''', (start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))
                
                columns = [desc[0] for desc in cursor.description]
                rows = cursor.fetchall()
                
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات اليومية: {e}")
            return []
    
    def get_platform_stats(self, days: int = 30) -> Dict[str, Dict]:
        """الحصول على إحصائيات المنصات"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT platform, 
                           COUNT(*) as total_posts,
                           SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_posts,
                           SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_posts,
                           AVG(response_time) as avg_response_time,
                           SUM(media_count) as total_media
                    FROM post_analytics 
                    WHERE posted_at >= ? AND posted_at <= ?
                    GROUP BY platform
                ''', (start_date.isoformat(), end_date.isoformat()))
                
                results = {}
                for row in cursor.fetchall():
                    platform = row[0]
                    results[platform] = {
                        'total_posts': row[1],
                        'successful_posts': row[2],
                        'failed_posts': row[3],
                        'success_rate': (row[2] / row[1] * 100) if row[1] > 0 else 0,
                        'avg_response_time': row[4] or 0,
                        'total_media': row[5]
                    }
                
                return results
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات المنصات: {e}")
            return {}
    
    def get_posting_patterns(self, days: int = 30) -> Dict[str, Any]:
        """تحليل أنماط النشر"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT posted_at, platform, success 
                    FROM post_analytics 
                    WHERE posted_at >= ? AND posted_at <= ?
                ''', (start_date.isoformat(), end_date.isoformat()))
                
                posts = cursor.fetchall()
                
                # تحليل الأنماط
                hourly_distribution = defaultdict(int)
                daily_distribution = defaultdict(int)
                platform_distribution = defaultdict(int)
                success_by_hour = defaultdict(list)
                
                for post in posts:
                    posted_time = datetime.fromisoformat(post[0])
                    hour = posted_time.hour
                    day = posted_time.strftime('%A')
                    platform = post[1]
                    success = post[2]
                    
                    hourly_distribution[hour] += 1
                    daily_distribution[day] += 1
                    platform_distribution[platform] += 1
                    success_by_hour[hour].append(success)
                
                # حساب معدل النجاح لكل ساعة
                success_rate_by_hour = {}
                for hour, successes in success_by_hour.items():
                    success_rate_by_hour[hour] = sum(successes) / len(successes) * 100
                
                return {
                    'hourly_distribution': dict(hourly_distribution),
                    'daily_distribution': dict(daily_distribution),
                    'platform_distribution': dict(platform_distribution),
                    'success_rate_by_hour': success_rate_by_hour,
                    'best_posting_hour': max(success_rate_by_hour.items(), 
                                           key=lambda x: x[1])[0] if success_rate_by_hour else None,
                    'most_active_day': max(daily_distribution.items(), 
                                         key=lambda x: x[1])[0] if daily_distribution else None
                }
                
        except Exception as e:
            logger.error(f"خطأ في تحليل أنماط النشر: {e}")
            return {}
    
    def get_content_analytics(self, days: int = 30) -> Dict[str, Any]:
        """تحليل المحتوى"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT content_length, media_count, hashtags_count, success, platform
                    FROM post_analytics 
                    WHERE posted_at >= ? AND posted_at <= ?
                ''', (start_date.isoformat(), end_date.isoformat()))
                
                posts = cursor.fetchall()
                
                if not posts:
                    return {}
                
                # تحليل المحتوى
                content_lengths = [post[0] for post in posts]
                media_counts = [post[1] for post in posts]
                hashtag_counts = [post[2] for post in posts]
                
                # تحليل العلاقة بين المحتوى والنجاح
                successful_posts = [post for post in posts if post[3]]
                failed_posts = [post for post in posts if not post[3]]
                
                return {
                    'avg_content_length': sum(content_lengths) / len(content_lengths),
                    'avg_media_count': sum(media_counts) / len(media_counts),
                    'avg_hashtag_count': sum(hashtag_counts) / len(hashtag_counts),
                    'successful_posts_avg_length': (
                        sum(post[0] for post in successful_posts) / len(successful_posts)
                        if successful_posts else 0
                    ),
                    'failed_posts_avg_length': (
                        sum(post[0] for post in failed_posts) / len(failed_posts)
                        if failed_posts else 0
                    ),
                    'posts_with_media_success_rate': (
                        len([post for post in successful_posts if post[1] > 0]) /
                        len([post for post in posts if post[1] > 0]) * 100
                        if any(post[1] > 0 for post in posts) else 0
                    )
                }
                
        except Exception as e:
            logger.error(f"خطأ في تحليل المحتوى: {e}")
            return {}
    
    def record_performance_metric(self, metric_name: str, value: float):
        """تسجيل مقياس أداء"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO performance_metrics (metric_name, metric_value, recorded_at)
                    VALUES (?, ?, ?)
                ''', (metric_name, value, datetime.now().isoformat()))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل مقياس الأداء: {e}")
    
    def get_performance_trends(self, metric_name: str, days: int = 7) -> List[Tuple]:
        """الحصول على اتجاهات الأداء"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT recorded_at, metric_value 
                    FROM performance_metrics 
                    WHERE metric_name = ? AND recorded_at >= ? AND recorded_at <= ?
                    ORDER BY recorded_at
                ''', (metric_name, start_date.isoformat(), end_date.isoformat()))
                
                return cursor.fetchall()
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على اتجاهات الأداء: {e}")
            return []
    
    def generate_summary_report(self, days: int = 30) -> Dict[str, Any]:
        """إنشاء تقرير ملخص شامل"""
        try:
            daily_stats = self.get_daily_stats(days)
            platform_stats = self.get_platform_stats(days)
            posting_patterns = self.get_posting_patterns(days)
            content_analytics = self.get_content_analytics(days)
            
            # حساب الإجماليات
            total_posts = sum(day.get('total_posts', 0) for day in daily_stats)
            total_successful = sum(day.get('successful_posts', 0) for day in daily_stats)
            total_failed = sum(day.get('failed_posts', 0) for day in daily_stats)
            
            return {
                'period_days': days,
                'generated_at': datetime.now().isoformat(),
                'summary': {
                    'total_posts': total_posts,
                    'successful_posts': total_successful,
                    'failed_posts': total_failed,
                    'success_rate': (total_successful / total_posts * 100) if total_posts > 0 else 0,
                    'avg_posts_per_day': total_posts / days if days > 0 else 0
                },
                'daily_stats': daily_stats,
                'platform_stats': platform_stats,
                'posting_patterns': posting_patterns,
                'content_analytics': content_analytics
            }
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير الملخص: {e}")
            return {}
    
    def export_analytics(self, file_path: str, days: int = 30) -> bool:
        """تصدير الإحصائيات لملف JSON"""
        try:
            report = self.generate_summary_report(days)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"تم تصدير الإحصائيات إلى: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تصدير الإحصائيات: {e}")
            return False

# إنشاء مثيل عام لمدير الإحصائيات
analytics_manager = AnalyticsManager()
