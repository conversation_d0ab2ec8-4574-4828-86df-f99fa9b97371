# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي
"""
import os
import shutil
import zipfile
import json
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Callable
from utils.logger import logger
from utils.config import config

class BackupManager:
    """مدير النسخ الاحتياطي"""
    
    def __init__(self):
        self.backup_dir = config.get('backup.directory', 'backups')
        self.auto_backup = config.get('backup.auto_enabled', True)
        self.backup_interval_hours = config.get('backup.interval_hours', 24)
        self.max_backups = config.get('backup.max_backups', 10)
        self.compress_backups = config.get('backup.compress', True)
        
        # الملفات والمجلدات المهمة للنسخ الاحتياطي
        self.important_files = [
            'config.json',
            'ayrshare_app.db',
            'analytics.db'
        ]
        
        self.important_directories = [
            'logs',
            'uploads',
            'thumbnails'
        ]
        
        # إنشاء مجلد النسخ الاحتياطي
        self._ensure_backup_directory()
    
    def _ensure_backup_directory(self):
        """التأكد من وجود مجلد النسخ الاحتياطي"""
        try:
            if not os.path.exists(self.backup_dir):
                os.makedirs(self.backup_dir)
                logger.info(f"تم إنشاء مجلد النسخ الاحتياطي: {self.backup_dir}")
        except Exception as e:
            logger.error(f"خطأ في إنشاء مجلد النسخ الاحتياطي: {e}")
    
    def create_backup(self, backup_name: Optional[str] = None, 
                     progress_callback: Optional[Callable] = None) -> Optional[str]:
        """إنشاء نسخة احتياطية"""
        try:
            # اسم النسخة الاحتياطية
            if not backup_name:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_name = f"backup_{timestamp}"
            
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            logger.info(f"بدء إنشاء نسخة احتياطية: {backup_name}")
            
            if self.compress_backups:
                # إنشاء نسخة مضغوطة
                zip_path = f"{backup_path}.zip"
                return self._create_compressed_backup(zip_path, progress_callback)
            else:
                # إنشاء نسخة غير مضغوطة
                return self._create_uncompressed_backup(backup_path, progress_callback)
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None
    
    def _create_compressed_backup(self, zip_path: str, 
                                progress_callback: Optional[Callable] = None) -> Optional[str]:
        """إنشاء نسخة احتياطية مضغوطة"""
        try:
            # حساب العدد الإجمالي للملفات
            total_files = len(self.important_files)
            for directory in self.important_directories:
                if os.path.exists(directory):
                    for root, dirs, files in os.walk(directory):
                        total_files += len(files)
            
            processed_files = 0
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # نسخ الملفات المهمة
                for file_path in self.important_files:
                    if os.path.exists(file_path):
                        zipf.write(file_path, file_path)
                        processed_files += 1
                        
                        if progress_callback:
                            progress = (processed_files / total_files) * 100
                            progress_callback(progress)
                
                # نسخ المجلدات المهمة
                for directory in self.important_directories:
                    if os.path.exists(directory):
                        for root, dirs, files in os.walk(directory):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arcname = os.path.relpath(file_path)
                                zipf.write(file_path, arcname)
                                processed_files += 1
                                
                                if progress_callback:
                                    progress = (processed_files / total_files) * 100
                                    progress_callback(progress)
                
                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    'created_at': datetime.now().isoformat(),
                    'version': config.get('app.version', '1.0.0'),
                    'files_count': processed_files,
                    'backup_type': 'compressed'
                }
                
                zipf.writestr('backup_info.json', 
                            json.dumps(backup_info, indent=2, ensure_ascii=False))
            
            logger.info(f"تم إنشاء نسخة احتياطية مضغوطة: {zip_path}")
            return zip_path
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية المضغوطة: {e}")
            return None
    
    def _create_uncompressed_backup(self, backup_path: str, 
                                  progress_callback: Optional[Callable] = None) -> Optional[str]:
        """إنشاء نسخة احتياطية غير مضغوطة"""
        try:
            os.makedirs(backup_path, exist_ok=True)
            
            # حساب العدد الإجمالي للملفات
            total_files = len(self.important_files)
            for directory in self.important_directories:
                if os.path.exists(directory):
                    for root, dirs, files in os.walk(directory):
                        total_files += len(files)
            
            processed_files = 0
            
            # نسخ الملفات المهمة
            for file_path in self.important_files:
                if os.path.exists(file_path):
                    dest_path = os.path.join(backup_path, file_path)
                    os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                    shutil.copy2(file_path, dest_path)
                    processed_files += 1
                    
                    if progress_callback:
                        progress = (processed_files / total_files) * 100
                        progress_callback(progress)
            
            # نسخ المجلدات المهمة
            for directory in self.important_directories:
                if os.path.exists(directory):
                    dest_dir = os.path.join(backup_path, directory)
                    shutil.copytree(directory, dest_dir, dirs_exist_ok=True)
                    
                    # تحديث التقدم
                    for root, dirs, files in os.walk(directory):
                        processed_files += len(files)
                        if progress_callback:
                            progress = min((processed_files / total_files) * 100, 100)
                            progress_callback(progress)
            
            # إنشاء ملف معلومات النسخة الاحتياطية
            backup_info = {
                'created_at': datetime.now().isoformat(),
                'version': config.get('app.version', '1.0.0'),
                'files_count': processed_files,
                'backup_type': 'uncompressed'
            }
            
            info_path = os.path.join(backup_path, 'backup_info.json')
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
            
            logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None
    
    def restore_backup(self, backup_path: str, 
                      progress_callback: Optional[Callable] = None) -> bool:
        """استعادة نسخة احتياطية"""
        try:
            logger.info(f"بدء استعادة النسخة الاحتياطية: {backup_path}")
            
            if backup_path.endswith('.zip'):
                return self._restore_compressed_backup(backup_path, progress_callback)
            else:
                return self._restore_uncompressed_backup(backup_path, progress_callback)
                
        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
    
    def _restore_compressed_backup(self, zip_path: str, 
                                 progress_callback: Optional[Callable] = None) -> bool:
        """استعادة نسخة احتياطية مضغوطة"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                file_list = zipf.namelist()
                total_files = len(file_list)
                
                for i, file_name in enumerate(file_list):
                    if file_name != 'backup_info.json':
                        zipf.extract(file_name, '.')
                    
                    if progress_callback:
                        progress = ((i + 1) / total_files) * 100
                        progress_callback(progress)
            
            logger.info(f"تم استعادة النسخة الاحتياطية المضغوطة: {zip_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية المضغوطة: {e}")
            return False
    
    def _restore_uncompressed_backup(self, backup_path: str, 
                                   progress_callback: Optional[Callable] = None) -> bool:
        """استعادة نسخة احتياطية غير مضغوطة"""
        try:
            # حساب العدد الإجمالي للملفات
            total_files = 0
            for root, dirs, files in os.walk(backup_path):
                total_files += len(files)
            
            processed_files = 0
            
            # نسخ الملفات من النسخة الاحتياطية
            for root, dirs, files in os.walk(backup_path):
                for file in files:
                    if file == 'backup_info.json':
                        continue
                    
                    src_path = os.path.join(root, file)
                    rel_path = os.path.relpath(src_path, backup_path)
                    dest_path = rel_path
                    
                    # إنشاء المجلد إذا لم يكن موجوداً
                    dest_dir = os.path.dirname(dest_path)
                    if dest_dir:
                        os.makedirs(dest_dir, exist_ok=True)
                    
                    shutil.copy2(src_path, dest_path)
                    processed_files += 1
                    
                    if progress_callback:
                        progress = (processed_files / total_files) * 100
                        progress_callback(progress)
            
            logger.info(f"تم استعادة النسخة الاحتياطية: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
    
    def list_backups(self) -> List[Dict]:
        """قائمة النسخ الاحتياطية المتاحة"""
        try:
            backups = []
            
            if not os.path.exists(self.backup_dir):
                return backups
            
            for item in os.listdir(self.backup_dir):
                item_path = os.path.join(self.backup_dir, item)
                
                backup_info = {
                    'name': item,
                    'path': item_path,
                    'created_at': datetime.fromtimestamp(os.path.getctime(item_path)),
                    'size_mb': self._get_size_mb(item_path),
                    'type': 'compressed' if item.endswith('.zip') else 'uncompressed'
                }
                
                # محاولة قراءة معلومات إضافية
                try:
                    if item.endswith('.zip'):
                        with zipfile.ZipFile(item_path, 'r') as zipf:
                            if 'backup_info.json' in zipf.namelist():
                                info_data = zipf.read('backup_info.json')
                                info = json.loads(info_data.decode('utf-8'))
                                backup_info.update(info)
                    else:
                        info_path = os.path.join(item_path, 'backup_info.json')
                        if os.path.exists(info_path):
                            with open(info_path, 'r', encoding='utf-8') as f:
                                info = json.load(f)
                                backup_info.update(info)
                except:
                    pass  # تجاهل الأخطاء في قراءة المعلومات الإضافية
                
                backups.append(backup_info)
            
            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backups.sort(key=lambda x: x['created_at'], reverse=True)
            
            return backups
            
        except Exception as e:
            logger.error(f"خطأ في قائمة النسخ الاحتياطية: {e}")
            return []
    
    def _get_size_mb(self, path: str) -> float:
        """حساب حجم الملف أو المجلد بالميجابايت"""
        try:
            if os.path.isfile(path):
                return os.path.getsize(path) / (1024 * 1024)
            elif os.path.isdir(path):
                total_size = 0
                for root, dirs, files in os.walk(path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        total_size += os.path.getsize(file_path)
                return total_size / (1024 * 1024)
            return 0
        except:
            return 0
    
    def delete_backup(self, backup_path: str) -> bool:
        """حذف نسخة احتياطية"""
        try:
            if os.path.isfile(backup_path):
                os.remove(backup_path)
            elif os.path.isdir(backup_path):
                shutil.rmtree(backup_path)
            
            logger.info(f"تم حذف النسخة الاحتياطية: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حذف النسخة الاحتياطية: {e}")
            return False
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.list_backups()
            
            if len(backups) > self.max_backups:
                # حذف النسخ الزائدة (الأقدم)
                backups_to_delete = backups[self.max_backups:]
                
                for backup in backups_to_delete:
                    self.delete_backup(backup['path'])
                
                logger.info(f"تم حذف {len(backups_to_delete)} نسخة احتياطية قديمة")
                
        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
    
    def auto_backup_worker(self):
        """عامل النسخ الاحتياطي التلقائي"""
        while self.auto_backup:
            try:
                # فحص إذا كان الوقت مناسب للنسخ الاحتياطي
                last_backup_time = config.get('backup.last_backup_time')
                
                if last_backup_time:
                    last_backup = datetime.fromisoformat(last_backup_time)
                    time_since_backup = datetime.now() - last_backup
                    
                    if time_since_backup.total_seconds() < (self.backup_interval_hours * 3600):
                        threading.Event().wait(3600)  # انتظار ساعة
                        continue
                
                # إنشاء نسخة احتياطية تلقائية
                backup_path = self.create_backup("auto_backup")
                
                if backup_path:
                    config.set('backup.last_backup_time', datetime.now().isoformat())
                    self.cleanup_old_backups()
                    logger.info("تم إنشاء نسخة احتياطية تلقائية")
                
                # انتظار حتى الفحص التالي
                threading.Event().wait(self.backup_interval_hours * 3600)
                
            except Exception as e:
                logger.error(f"خطأ في النسخ الاحتياطي التلقائي: {e}")
                threading.Event().wait(3600)  # انتظار ساعة عند الخطأ
    
    def start_auto_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        if self.auto_backup:
            backup_thread = threading.Thread(target=self.auto_backup_worker, daemon=True)
            backup_thread.start()
            logger.info("تم بدء النسخ الاحتياطي التلقائي")
    
    def enable_auto_backup(self, enabled: bool = True):
        """تفعيل أو إلغاء النسخ الاحتياطي التلقائي"""
        self.auto_backup = enabled
        config.set('backup.auto_enabled', enabled)
        
        if enabled:
            self.start_auto_backup()
        
        logger.info(f"النسخ الاحتياطي التلقائي: {'مفعل' if enabled else 'معطل'}")

# إنشاء مثيل عام لمدير النسخ الاحتياطي
backup_manager = BackupManager()
