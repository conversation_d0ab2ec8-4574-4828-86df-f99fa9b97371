# -*- coding: utf-8 -*-
"""
اختبار إصلاح رسائل خطأ API Key
"""
import tkinter as tk
import customtkinter as ctk
from tkinter import messagebox

def test_api_error_fix():
    """اختبار إصلاح رسائل خطأ API Key"""
    print("🧪 اختبار إصلاح رسائل خطأ API Key")
    print("=" * 50)
    
    # إنشاء نافذة اختبار
    root = ctk.CTk()
    root.title("اختبار إصلاح رسائل خطأ API Key")
    root.geometry("800x600")
    
    # إنشاء الإطار الرئيسي
    main_frame = ctk.CTkScrollableFrame(root)
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    ctk.CTkLabel(main_frame, text="اختبار رسائل خطأ API Key المحسنة", 
                font=("Arial", 18, "bold")).pack(pady=10)
    
    # حقل API Key
    api_frame = ctk.CTkFrame(main_frame)
    api_frame.pack(fill="x", padx=10, pady=10)
    
    ctk.CTkLabel(api_frame, text="API Key:", width=100).pack(side="left", padx=5)
    api_key_var = tk.StringVar()
    api_key_entry = ctk.CTkEntry(api_frame, textvariable=api_key_var, width=400)
    api_key_entry.pack(side="left", padx=5, fill="x", expand=True)
    
    # شريط الحالة
    status_frame = ctk.CTkFrame(main_frame)
    status_frame.pack(fill="x", padx=10, pady=5)
    
    status_label = ctk.CTkLabel(status_frame, text="جاهز للاختبار")
    status_label.pack(pady=5)
    
    # وظائف محاكاة أخطاء API
    def test_empty_api_key():
        """اختبار API Key فارغ"""
        api_key_var.set("")
        status_label.configure(text="❌ يرجى إدخال API Key")
        messagebox.showwarning("تحذير", "يرجى إدخال API Key صالح أولاً")
    
    def test_invalid_api_key():
        """اختبار API Key غير صالح"""
        api_key_var.set("invalid_key_123")
        status_label.configure(text="❌ API Key غير صالح")
        
        error_msg = """🔑 API Key غير صالح!

❌ المشكلة:
API Key الذي أدخلته غير صحيح أو منتهي الصلاحية

✅ الحل:
1. تأكد من نسخ API Key بالكامل (بدون مسافات)
2. تأكد أن API Key لم تنته صلاحيته
3. تأكد أن حسابك نشط في Ayrshare

🔗 للحصول على API Key صالح:
• اذهب إلى: https://app.ayrshare.com
• سجل دخول لحسابك
• اذهب لقسم "API Key" أو "Settings"
• انسخ API Key الجديد بالكامل
• الصقه في التطبيق باستخدام Ctrl+V

💡 نصيحة: استخدم النسخ واللصق لتجنب الأخطاء"""
        messagebox.showerror("🔑 API Key غير صالح", error_msg)
    
    def test_connection_error():
        """اختبار خطأ الاتصال بالإنترنت"""
        status_label.configure(text="❌ لا يمكن الاتصال بالإنترنت")
        
        error_msg = """🌐 مشكلة في الاتصال بالإنترنت!

❌ المشكلة:
لا يمكن الوصول إلى خوادم Ayrshare

✅ الحل:
1. تحقق من اتصالك بالإنترنت
2. تأكد أن الجدار الناري لا يحجب التطبيق
3. جرب إعادة تشغيل الراوتر
4. جرب مرة أخرى بعد قليل"""
        messagebox.showerror("🌐 مشكلة في الإنترنت", error_msg)
    
    def test_timeout_error():
        """اختبار انتهاء مهلة الاتصال"""
        status_label.configure(text="❌ انتهت مهلة الاتصال")
        
        error_msg = """⏰ انتهت مهلة الاتصال!

❌ المشكلة:
الاتصال بطيء جداً أو متقطع

✅ الحل:
1. تحقق من سرعة الإنترنت
2. جرب مرة أخرى بعد قليل
3. تأكد من عدم وجود تطبيقات تستهلك الإنترنت"""
        messagebox.showerror("⏰ انتهت مهلة الاتصال", error_msg)
    
    def test_rate_limit_error():
        """اختبار تجاوز حدود الطلبات"""
        status_label.configure(text="❌ تجاوز حدود الطلبات")
        
        error_msg = """🚫 تجاوز حدود الطلبات!

❌ المشكلة:
تم إرسال طلبات كثيرة جداً في وقت قصير

✅ الحل:
1. انتظر 5-10 دقائق ثم جرب مرة أخرى
2. تجنب الضغط على "اختبار" عدة مرات متتالية
3. راجع حدود خطتك في Ayrshare"""
        messagebox.showerror("🚫 تجاوز حدود الطلبات", error_msg)
    
    def test_server_error():
        """اختبار خطأ الخادم"""
        status_label.configure(text="❌ خطأ في خادم Ayrshare")
        
        error_msg = """🔧 مشكلة في خادم Ayrshare!

❌ المشكلة:
خطأ مؤقت في خوادم Ayrshare

✅ الحل:
1. جرب مرة أخرى بعد 5-10 دقائق
2. تحقق من حالة خدمة Ayrshare على موقعهم
3. إذا استمرت المشكلة، تواصل مع دعم Ayrshare"""
        messagebox.showerror("🔧 مشكلة في الخادم", error_msg)
    
    def test_success():
        """اختبار نجاح الاتصال"""
        api_key_var.set("valid_api_key_example")
        status_label.configure(text="✅ تم الاتصال بنجاح")
        messagebox.showinfo("نجح الاتصال", "تم الاتصال بـ Ayrshare API بنجاح!")
    
    # أزرار الاختبار
    buttons_frame = ctk.CTkFrame(main_frame)
    buttons_frame.pack(fill="x", padx=10, pady=10)
    
    ctk.CTkLabel(buttons_frame, text="اختبار أنواع الأخطاء:", 
                font=("Arial", 14, "bold")).pack(pady=5)
    
    # الصف الأول من الأزرار
    row1_frame = ctk.CTkFrame(buttons_frame)
    row1_frame.pack(fill="x", padx=5, pady=2)
    
    ctk.CTkButton(row1_frame, text="API Key فارغ", 
                 command=test_empty_api_key).pack(side="left", padx=5)
    ctk.CTkButton(row1_frame, text="API Key غير صالح", 
                 command=test_invalid_api_key).pack(side="left", padx=5)
    ctk.CTkButton(row1_frame, text="مشكلة إنترنت", 
                 command=test_connection_error).pack(side="left", padx=5)
    
    # الصف الثاني من الأزرار
    row2_frame = ctk.CTkFrame(buttons_frame)
    row2_frame.pack(fill="x", padx=5, pady=2)
    
    ctk.CTkButton(row2_frame, text="انتهاء مهلة", 
                 command=test_timeout_error).pack(side="left", padx=5)
    ctk.CTkButton(row2_frame, text="تجاوز حدود", 
                 command=test_rate_limit_error).pack(side="left", padx=5)
    ctk.CTkButton(row2_frame, text="خطأ خادم", 
                 command=test_server_error).pack(side="left", padx=5)
    
    # الصف الثالث من الأزرار
    row3_frame = ctk.CTkFrame(buttons_frame)
    row3_frame.pack(fill="x", padx=5, pady=2)
    
    ctk.CTkButton(row3_frame, text="✅ نجاح الاتصال", 
                 command=test_success).pack(side="left", padx=5)
    
    def clear_status():
        status_label.configure(text="جاهز للاختبار")
        api_key_var.set("")
    
    ctk.CTkButton(row3_frame, text="مسح", 
                 command=clear_status).pack(side="left", padx=5)
    
    # معلومات الاختبار
    info_frame = ctk.CTkFrame(main_frame)
    info_frame.pack(fill="x", padx=10, pady=10)
    
    info_text = """🎯 تعليمات الاختبار:

1. اضغط على أي زر لاختبار نوع خطأ معين
2. ستظهر رسالة خطأ محسنة مع:
   • وصف واضح للمشكلة
   • خطوات الحل المقترحة
   • نصائح مفيدة
   • رموز تعبيرية للوضوح

3. قارن الرسائل الجديدة مع الرسائل القديمة:
   • الرسائل الجديدة: واضحة ومفيدة باللغة العربية
   • الرسائل القديمة: مربكة وباللغة الإنجليزية

✅ إذا كانت الرسائل واضحة ومفيدة، فالإصلاح نجح!

💡 الهدف: تحويل رسائل الخطأ المربكة إلى إرشادات مفيدة"""
    
    ctk.CTkLabel(info_frame, text=info_text, justify="left").pack(pady=10)
    
    def close_test():
        print("✅ اختبار إصلاح رسائل خطأ API Key مكتمل")
        root.destroy()
    
    # زر الإغلاق
    close_frame = ctk.CTkFrame(main_frame)
    close_frame.pack(fill="x", padx=10, pady=10)
    
    ctk.CTkButton(close_frame, text="إغلاق الاختبار", 
                 command=close_test).pack(pady=5)
    
    print("🎯 تعليمات الاختبار:")
    print("1. اضغط على أزرار الاختبار المختلفة")
    print("2. لاحظ الرسائل المحسنة والواضحة")
    print("3. قارن مع الرسائل القديمة المربكة")
    print("4. تأكد أن كل رسالة تحتوي على حل واضح")
    print("\n✅ إذا كانت الرسائل واضحة، فالإصلاح نجح!")
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    try:
        test_api_error_fix()
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
