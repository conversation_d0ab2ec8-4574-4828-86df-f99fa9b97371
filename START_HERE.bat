@echo off
chcp 65001 >nul
title ابدأ هنا - Ayrshare Social Media Manager

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║                    🎉 مرحباً بك! 🎉                          ║
echo ║                                                              ║
echo ║           Ayrshare Social Media Manager                      ║
echo ║                                                              ║
echo ║              برنامج إدارة وسائل التواصل الاجتماعي              ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 مرحباً بك في Ayrshare Social Media Manager!
echo.
echo هذا البرنامج يساعدك على:
echo ✅ نشر المحتوى على Facebook, Instagram, TikTok
echo ✅ جدولة المنشورات
echo ✅ إدارة الوسائط (صور وفيديوهات)
echo ✅ تخطي حدود المنصات
echo.

echo 📋 قبل البدء، تحتاج إلى:
echo 1. حساب على Ayrshare.com
echo 2. API Key من لوحة تحكم Ayrshare
echo 3. ربط حساباتك على المنصات المطلوبة
echo.

echo 🔗 للحصول على API Key:
echo 1. اذهب إلى: https://app.ayrshare.com
echo 2. أنشئ حساب أو سجل دخول
echo 3. اذهب إلى قسم "API"
echo 4. انسخ API Key
echo 5. اربط حساباتك على Facebook, Instagram, TikTok
echo.

echo ⚡ اختر ما تريد فعله:
echo.
echo [1] تشغيل البرنامج مباشرة
echo [2] تثبيت المتطلبات أولاً
echo [3] اختبار النظام
echo [4] قراءة دليل الاستخدام
echo [5] الخروج
echo.

set /p choice="أدخل اختيارك (1-5): "

if "%choice%"=="1" goto run_app
if "%choice%"=="2" goto install_deps
if "%choice%"=="3" goto test_system
if "%choice%"=="4" goto show_help
if "%choice%"=="5" goto exit
goto invalid_choice

:run_app
echo.
echo 🚀 جاري تشغيل البرنامج...
python start.py
goto end

:install_deps
echo.
echo 📦 جاري تثبيت المتطلبات...
python -m pip install --upgrade pip
pip install -r requirements.txt
echo.
echo ✅ تم تثبيت المتطلبات!
echo الآن يمكنك تشغيل البرنامج
pause
goto main_menu

:test_system
echo.
echo 🧪 جاري اختبار النظام...
python simple_test.py
echo.
pause
goto main_menu

:show_help
echo.
echo 📚 فتح دليل الاستخدام...
if exist "README.md" (
    start README.md
) else (
    echo ملف README.md غير موجود
)
if exist "QUICK_START.md" (
    start QUICK_START.md
) else (
    echo ملف QUICK_START.md غير موجود
)
echo.
pause
goto main_menu

:invalid_choice
echo.
echo ❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى
pause
goto main_menu

:main_menu
cls
goto start

:exit
echo.
echo 👋 شكراً لك!
echo.

:end
pause
