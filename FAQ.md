# الأسئلة الشائعة - FAQ

## 🚀 البدء والتثبيت

### س: ما هي متطلبات تشغيل البرنامج؟
**ج:** تحتاج إلى:
- Python 3.10 أو أحدث
- نظام Windows 10/11, macOS 10.14+, أو Linux
- 4GB RAM (مستحسن)
- اتصال إنترنت مستقر
- حساب Ayrshare صالح

### س: كيف أحصل على API Key؟
**ج:** 
1. اذهب إلى [app.ayrshare.com](https://app.ayrshare.com)
2. أنشئ حساب جديد أو سجل دخول
3. اذهب إلى قسم "API" في لوحة التحكم
4. انسخ API Key الخاص بك
5. اربط حساباتك على المنصات المطلوبة

### س: هل البرنامج مجاني؟
**ج:** البرنامج نفسه مجاني، لكن تحتاج اشتراك Ayrshare للوصول إلى API. Ayrshare يقدم خطة مجانية محدودة وخطط مدفوعة.

### س: لماذا يظهر خطأ "فشل الاتصال"؟
**ج:** تأكد من:
- صحة API Key
- اتصال الإنترنت
- ربط الحسابات في Ayrshare Dashboard
- عدم انتهاء صلاحية الاشتراك

## 📱 المنصات والحسابات

### س: ما المنصات المدعومة؟
**ج:** حالياً يدعم البرنامج:
- Facebook Pages
- Instagram Business Accounts
- TikTok Business Accounts

### س: هل يمكن ربط حسابات شخصية؟
**ج:** لا، يجب استخدام:
- Facebook Pages (وليس الحسابات الشخصية)
- Instagram Business Accounts
- TikTok Business Accounts

### س: كيف أربط حساباتي؟
**ج:**
1. اذهب إلى [Ayrshare Dashboard](https://app.ayrshare.com)
2. اضغط "Connect Social Accounts"
3. اتبع التعليمات لكل منصة
4. تأكد من الأذونات المطلوبة

### س: هل يمكن استخدام حسابات متعددة؟
**ج:** نعم، باستخدام Profile Keys. كل Profile Key يمثل مجموعة حسابات منفصلة.

## 📝 النشر والمحتوى

### س: ما أنواع المحتوى المدعومة؟
**ج:**
- النصوص
- الصور (JPG, PNG, GIF, BMP)
- الفيديوهات (MP4, MOV, AVI, MKV)
- الريلز (Facebook, Instagram)
- الستوريز (Instagram)

### س: ما الحد الأقصى لحجم الملفات؟
**ج:**
- الصور: حتى 10MB
- الفيديوهات: حتى 100MB
- يمكن تعديل هذه الحدود في الإعدادات

### س: لماذا فشل رفع الفيديو؟
**ج:** تأكد من:
- حجم الفيديو أقل من 100MB
- صيغة الفيديو مدعومة
- جودة الإنترنت مستقرة
- عدم تلف الملف

### س: كيف أضيف هاشتاجات تلقائياً؟
**ج:**
1. اذهب إلى تبويب "الإعدادات"
2. اضبط الهاشتاجات الافتراضية لكل منصة
3. ستضاف تلقائياً للمنشورات الجديدة

## ⏰ الجدولة

### س: كيف أجدول منشور؟
**ج:**
1. اكتب المحتوى
2. ضع علامة على "جدولة المنشور"
3. أدخل التاريخ والوقت
4. اضغط "حفظ كمسودة"

### س: هل يعمل النشر المجدول إذا أغلقت البرنامج؟
**ج:** لا، يجب أن يكون البرنامج يعمل في الوقت المحدد للنشر. يمكنك تصغيره لشريط المهام.

### س: كيف أعدل وقت منشور مجدول؟
**ج:**
1. اذهب إلى تبويب "الجدولة"
2. اختر المنشور المطلوب
3. اضغط "تعديل الوقت"
4. أدخل الوقت الجديد

### س: هل يمكن تكرار المنشورات؟
**ج:** هذه الميزة قيد التطوير وستكون متاحة في التحديث القادم.

## 🔄 تخطي الحدود

### س: ما هو تخطي حدود المنصات؟
**ج:** ميزة تسمح بتجاوز حدود النشر المفروضة من المنصات، مع تأخير ذكي لتجنب الحظر.

### س: هل تخطي الحدود آمن؟
**ج:** نعم، إذا استُخدم بحذر. البرنامج يضيف تأخير بين المنشورات ويراقب الاستخدام.

### س: كيف أفعل تخطي الحدود؟
**ج:**
1. اذهب إلى تبويب "الإعدادات"
2. فعل "تخطي حدود المنصات"
3. اضبط التأخير المناسب (60 ثانية مستحسن)

### س: ما التأخير المناسب بين المنشورات؟
**ج:** يُنصح بـ 60-120 ثانية لتجنب اكتشاف الأتمتة.

## 🔧 المشاكل التقنية

### س: البرنامج لا يفتح، ماذا أفعل؟
**ج:**
1. تأكد من تثبيت Python 3.10+
2. شغل `pip install -r requirements.txt`
3. شغل `python test_app.py` للتحقق من المشاكل
4. راجع ملفات السجل في مجلد `logs/`

### س: رسالة "خطأ في استيراد المكونات"؟
**ج:**
1. شغل `pip install -r requirements.txt --upgrade`
2. تأكد من إصدار Python
3. أعد تشغيل البرنامج
4. تحقق من مساحة القرص

### س: كيف أحدث البرنامج؟
**ج:**
1. احفظ نسخة احتياطية من `config.json`
2. حمل الإصدار الجديد
3. شغل `pip install -r requirements.txt --upgrade`
4. استعد ملف الإعدادات

### س: أين أجد ملفات السجل؟
**ج:** في مجلد `logs/` بجانب البرنامج. كل يوم له ملف منفصل.

## 💾 البيانات والإعدادات

### س: أين تُحفظ بياناتي؟
**ج:**
- الإعدادات: `config.json`
- قاعدة البيانات: `ayrshare_app.db`
- السجلات: مجلد `logs/`

### س: كيف أنسخ إعداداتي لجهاز آخر؟
**ج:**
1. انسخ ملف `config.json`
2. انسخ ملف `ayrshare_app.db` (اختياري)
3. ضعهما في مجلد البرنامج الجديد

### س: كيف أحذف جميع البيانات؟
**ج:**
1. احذف ملف `config.json`
2. احذف ملف `ayrshare_app.db`
3. احذف مجلد `logs/`
4. أعد تشغيل البرنامج

### س: هل بياناتي آمنة؟
**ج:** نعم، جميع البيانات محفوظة محلياً على جهازك. API Key محفوظ بشكل آمن في ملف الإعدادات.

## 📊 الإحصائيات

### س: كيف أراقب استخدامي؟
**ج:** اذهب إلى تبويب "الإحصائيات" لرؤية:
- الاستخدام الساعي واليومي
- عدد المنشورات لكل منصة
- حالة الحدود

### س: ما معنى "تم الوصول للحد الأقصى"؟
**ج:** وصلت لحد النشر المسموح. انتظر حتى إعادة تعيين الحد أو فعل تخطي الحدود.

### س: كيف أعرف متى يُعاد تعيين الحد؟
**ج:** الحدود الساعية تُعاد كل ساعة، واليومية كل 24 ساعة من أول منشور.

## 🆘 الدعم

### س: أحتاج مساعدة إضافية؟
**ج:**
1. راجع ملف `README.md` للدليل الكامل
2. شغل `python test_app.py` للتشخيص
3. راجع ملفات السجل للأخطاء
4. تأكد من تحديث البرنامج

### س: كيف أبلغ عن خطأ؟
**ج:**
1. اجمع معلومات الخطأ من السجلات
2. اكتب خطوات إعادة إنتاج المشكلة
3. أرفق لقطة شاشة إن أمكن
4. تواصل مع المطور

### س: هل يمكن طلب ميزة جديدة؟
**ج:** نعم! نرحب بالاقتراحات. اكتب وصف مفصل للميزة المطلوبة وفائدتها.

---

**لم تجد إجابة لسؤالك؟** راجع ملف `README.md` أو `FEATURES.md` للمزيد من التفاصيل.
